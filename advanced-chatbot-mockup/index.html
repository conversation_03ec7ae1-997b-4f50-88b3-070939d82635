<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">🤖</div>
                    <h2>AI Dashboard</h2>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 12h18M3 6h18M3 18h18"/>
                    </svg>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <h3>AI Agents</h3>
                    <ul class="agent-list" id="agentList">
                        <!-- Agents will be populated by JavaScript -->
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h3>Quick Actions</h3>
                    <ul class="quick-actions">
                        <li><button class="nav-item" id="analyticsBtn">📊 Analytics</button></li>
                        <li><button class="nav-item" id="settingsBtn">⚙️ Settings</button></li>
                        <li><button class="nav-item" id="workflowBtn">🔄 Workflows</button></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="header">
                <div class="header-left">
                    <h1 class="current-agent-title" id="currentAgentTitle">Customer Service Agent</h1>
                    <span class="status-indicator online">● Online</span>
                </div>
                
                <div class="header-right">
                    <button class="theme-toggle" id="themeToggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"/>
                            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                        </svg>
                    </button>
                    
                    <div class="user-menu">
                        <button class="user-avatar">
                            <div class="avatar-circle">JD</div>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Chat Interface -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be populated by JavaScript -->
                </div>
                
                <div class="chat-input-container">
                    <div class="typing-indicator hidden" id="typingIndicator">
                        <span>AI is typing</span>
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                    
                    <div class="chat-input-wrapper">
                        <div class="input-actions">
                            <button class="input-btn" id="voiceBtn" title="Voice Input">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3zM19 10v2a7 7 0 0 1-14 0v-2M12 19v4M8 23h8"/>
                                </svg>
                            </button>
                            
                            <button class="input-btn" id="fileBtn" title="Attach File">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                                </svg>
                            </button>
                        </div>
                        
                        <textarea class="chat-input" id="chatInput" placeholder="Type your message..." rows="1"></textarea>
                        
                        <button class="send-btn" id="sendBtn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="quick-responses" id="quickResponses">
                        <!-- Quick response buttons will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>

        <!-- Right Sidebar - Advanced Features -->
        <aside class="right-sidebar" id="rightSidebar">
            <div class="sidebar-tabs">
                <button class="tab-btn active" data-tab="analytics">Analytics</button>
                <button class="tab-btn" data-tab="workflows">Workflows</button>
                <button class="tab-btn" data-tab="settings">Settings</button>
            </div>
            
            <!-- Analytics Tab -->
            <div class="tab-content active" id="analyticsTab">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="activeConversations">247</div>
                        <div class="metric-label">Active Conversations</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value" id="resolvedToday">1,834</div>
                        <div class="metric-label">Resolved Today</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value" id="avgResponseTime">1.2s</div>
                        <div class="metric-label">Avg Response Time</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-value" id="satisfactionScore">4.8⭐</div>
                        <div class="metric-label">Satisfaction Score</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3>Performance Trends</h3>
                    <canvas id="performanceChart" width="400" height="200"></canvas>
                </div>
                
                <div class="insights-panel">
                    <h3>AI Insights</h3>
                    <div class="insight-item">
                        <span class="insight-icon">🔮</span>
                        <div class="insight-content">
                            <p>Predicted 23% increase in support requests next week</p>
                            <span class="insight-confidence">87% confidence</span>
                        </div>
                    </div>
                    
                    <div class="insight-item">
                        <span class="insight-icon">📈</span>
                        <div class="insight-content">
                            <p>Customer satisfaction improved 12% this month</p>
                            <span class="insight-confidence">95% confidence</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Workflows Tab -->
            <div class="tab-content" id="workflowsTab">
                <div class="workflow-item">
                    <div class="workflow-header">
                        <h4>Auto-Escalation</h4>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <p>Automatically escalate complex issues to human agents</p>
                </div>
                
                <div class="workflow-item">
                    <div class="workflow-header">
                        <h4>Smart Routing</h4>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <p>Route conversations to specialized agents based on context</p>
                </div>
                
                <div class="workflow-item">
                    <div class="workflow-header">
                        <h4>Predictive Responses</h4>
                        <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <p>Generate proactive responses based on user behavior</p>
                </div>
            </div>
            
            <!-- Settings Tab -->
            <div class="tab-content" id="settingsTab">
                <div class="settings-section">
                    <h4>General Settings</h4>
                    
                    <div class="setting-item">
                        <label>Response Speed</label>
                        <select class="form-control">
                            <option>Fast (1-2s)</option>
                            <option selected>Normal (2-3s)</option>
                            <option>Slow (3-5s)</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label>Language</label>
                        <select class="form-control">
                            <option selected>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <label>Personality</label>
                        <select class="form-control">
                            <option>Professional</option>
                            <option selected>Friendly</option>
                            <option>Casual</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>Privacy & Security</h4>
                    
                    <div class="setting-item">
                        <div class="setting-row">
                            <span>Data Encryption</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-row">
                            <span>Anonymous Mode</span>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script src="app.js"></script>
</body>
</html>