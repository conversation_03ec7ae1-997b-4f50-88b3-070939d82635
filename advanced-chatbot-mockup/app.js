// Application Data
const appData = {
  agents: [
    {
      id: "customer-service",
      name: "Customer Service",
      icon: "🎧",
      description: "24/7 customer support with context awareness",
      capabilities: ["FAQ Resolution", "Order Tracking", "Issue Escalation", "Sentiment Analysis"],
      color: "blue"
    },
    {
      id: "sales-marketing",
      name: "Sales & Marketing",
      icon: "📊",
      description: "Lead generation and sales automation",
      capabilities: ["Lead Qualification", "Product Recommendations", "Campaign Management", "ROI Analytics"],
      color: "green"
    },
    {
      id: "hr-operations",
      name: "HR Operations",
      icon: "👥",
      description: "Employee support and HR automation",
      capabilities: ["Policy Queries", "Leave Management", "Onboarding", "Performance Reviews"],
      color: "purple"
    },
    {
      id: "it-support",
      name: "IT Support",
      icon: "💻",
      description: "Technical support and system management",
      capabilities: ["Troubleshooting", "System Monitoring", "Security Alerts", "Automated Fixes"],
      color: "red"
    },
    {
      id: "research-analytics",
      name: "Research & Analytics",
      icon: "🔍",
      description: "Data analysis and business intelligence",
      capabilities: ["Market Research", "Data Visualization", "Predictive Modeling", "Report Generation"],
      color: "indigo"
    },
    {
      id: "creative-writing",
      name: "Creative Writing",
      icon: "✍️",
      description: "Content creation and copywriting",
      capabilities: ["Content Generation", "Brand Voice", "SEO Optimization", "A/B Testing"],
      color: "pink"
    },
    {
      id: "code-assistant",
      name: "Code Assistant",
      icon: "⚡",
      description: "Development support and code review",
      capabilities: ["Code Review", "Bug Detection", "Performance Optimization", "Documentation"],
      color: "yellow"
    },
    {
      id: "predictive-ai",
      name: "Predictive AI",
      icon: "🔮",
      description: "Autonomous predictions and insights",
      capabilities: ["Trend Forecasting", "Risk Assessment", "Automated Workflows", "Proactive Alerts"],
      color: "teal"
    }
  ],
  sampleConversations: {
    "customer-service": [
      {
        type: "user",
        message: "I need help with my recent order #12345",
        timestamp: "2025-07-15T23:30:00"
      },
      {
        type: "bot",
        message: "I can help you with order #12345. Let me check the status... Your order was shipped yesterday and should arrive by July 17th. I can see it's currently in transit. Would you like me to set up delivery notifications?",
        timestamp: "2025-07-15T23:30:05",
        sentiment: "helpful",
        confidence: 0.95
      }
    ],
    "sales-marketing": [
      {
        type: "user",
        message: "What's our conversion rate for the latest campaign?",
        timestamp: "2025-07-15T23:28:00"
      },
      {
        type: "bot",
        message: "Your latest email campaign has a 24.5% open rate and 3.2% click-through rate, which is 15% above industry average. I've identified 3 high-potential leads that warrant immediate follow-up. Would you like me to draft personalized outreach messages?",
        timestamp: "2025-07-15T23:28:03",
        analytics: {
          open_rate: 24.5,
          ctr: 3.2,
          conversion_rate: 12.8
        }
      }
    ],
    "hr-operations": [
      {
        type: "user",
        message: "Can you help me with the new employee onboarding process?",
        timestamp: "2025-07-15T23:25:00"
      },
      {
        type: "bot",
        message: "I'd be happy to help with onboarding! I can guide you through our automated onboarding workflow, which includes document collection, system access setup, and training schedule creation. Which aspect would you like to start with?",
        timestamp: "2025-07-15T23:25:03"
      }
    ],
    "it-support": [
      {
        type: "user",
        message: "My computer is running very slowly today",
        timestamp: "2025-07-15T23:22:00"
      },
      {
        type: "bot",
        message: "I can help diagnose the performance issue. I'm running a system scan now... I've detected high memory usage from multiple browser tabs and a background process consuming 40% CPU. I can automatically optimize this for you, or walk you through manual steps. What would you prefer?",
        timestamp: "2025-07-15T23:22:05"
      }
    ],
    "research-analytics": [
      {
        type: "user",
        message: "I need market research on sustainable packaging trends",
        timestamp: "2025-07-15T23:20:00"
      },
      {
        type: "bot",
        message: "I've compiled comprehensive market research on sustainable packaging trends. The market is projected to grow 14.3% CAGR through 2028, driven by consumer demand and regulatory changes. I can generate detailed reports on materials innovation, competitive analysis, and consumer sentiment. Which area interests you most?",
        timestamp: "2025-07-15T23:20:08"
      }
    ],
    "creative-writing": [
      {
        type: "user",
        message: "I need help writing a product launch announcement",
        timestamp: "2025-07-15T23:18:00"
      },
      {
        type: "bot",
        message: "I'd love to help craft your product launch announcement! I can create multiple variations optimized for different audiences and channels. To get started, could you tell me about the product and your target audience? I'll ensure the tone matches your brand voice perfectly.",
        timestamp: "2025-07-15T23:18:04"
      }
    ],
    "code-assistant": [
      {
        type: "user",
        message: "Can you review this Python function for optimization?",
        timestamp: "2025-07-15T23:15:00"
      },
      {
        type: "bot",
        message: "I'd be happy to review your Python function for optimization! Please share the code, and I'll analyze it for performance improvements, best practices, and potential bugs. I can also suggest more efficient algorithms or data structures if applicable.",
        timestamp: "2025-07-15T23:15:03"
      }
    ],
    "predictive-ai": [
      {
        type: "bot",
        message: "🔮 Predictive Insight: Based on current trends, I forecast a 23% increase in customer service inquiries next week due to the upcoming product launch. I've automatically scaled your support team schedule and prepared FAQ responses for common launch-related questions.",
        timestamp: "2025-07-15T23:25:00",
        type: "prediction",
        confidence: 0.87,
        actions: ["Schedule scaling", "FAQ preparation", "Alert management"]
      }
    ]
  },
  metrics: {
    activeConversations: 247,
    resolvedToday: 1834,
    avgResponseTime: "1.2s",
    satisfactionScore: 4.8,
    automationRate: 73,
    costSavings: 156800
  }
};

// Application State
let currentAgent = 'customer-service';
let conversationHistory = {};
let isTyping = false;
let darkMode = false;

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing application...');
  
  // Initialize components
  initializeAgents();
  initializeTabs();
  initializeChat();
  initializeEventListeners();
  initializeChart();
  updateMetrics();
  
  // Initialize with customer service agent
  switchAgent('customer-service');
  
  console.log('Application initialized successfully');
});

// Initialize Agents
function initializeAgents() {
  const agentList = document.getElementById('agentList');
  if (!agentList) {
    console.error('Agent list element not found');
    return;
  }
  
  agentList.innerHTML = '';
  
  appData.agents.forEach(agent => {
    const agentElement = document.createElement('li');
    agentElement.innerHTML = `
      <button class="agent-item" data-agent="${agent.id}">
        <div class="agent-icon">${agent.icon}</div>
        <div class="agent-info">
          <div class="agent-name">${agent.name}</div>
          <div class="agent-description">${agent.description}</div>
        </div>
      </button>
    `;
    agentList.appendChild(agentElement);
  });
  
  console.log('Agents initialized');
}

// Initialize Tabs
function initializeTabs() {
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetTab = button.dataset.tab;
      
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // Add active class to clicked button and corresponding content
      button.classList.add('active');
      const targetContent = document.getElementById(targetTab + 'Tab');
      if (targetContent) {
        targetContent.classList.add('active');
      }
    });
  });
  
  console.log('Tabs initialized');
}

// Initialize Chat
function initializeChat() {
  const chatInput = document.getElementById('chatInput');
  const sendBtn = document.getElementById('sendBtn');
  
  if (!chatInput || !sendBtn) {
    console.error('Chat elements not found');
    return;
  }
  
  chatInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    
    // Enable/disable send button
    sendBtn.disabled = !this.value.trim();
  });
  
  chatInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });
  
  console.log('Chat initialized');
}

// Initialize Event Listeners
function initializeEventListeners() {
  // Agent switching
  const agentList = document.getElementById('agentList');
  if (agentList) {
    agentList.addEventListener('click', function(e) {
      const agentButton = e.target.closest('.agent-item');
      if (agentButton) {
        const agentId = agentButton.dataset.agent;
        console.log('Switching to agent:', agentId);
        switchAgent(agentId);
      }
    });
  }
  
  // Send message
  const sendBtn = document.getElementById('sendBtn');
  if (sendBtn) {
    sendBtn.addEventListener('click', sendMessage);
  }
  
  // Theme toggle
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
  }
  
  // Sidebar toggle
  const sidebarToggle = document.getElementById('sidebarToggle');
  if (sidebarToggle) {
    sidebarToggle.addEventListener('click', toggleSidebar);
  }
  
  // Mobile overlay
  const mobileOverlay = document.getElementById('mobileOverlay');
  if (mobileOverlay) {
    mobileOverlay.addEventListener('click', closeMobileMenus);
  }
  
  // Voice input simulation
  const voiceBtn = document.getElementById('voiceBtn');
  if (voiceBtn) {
    voiceBtn.addEventListener('click', simulateVoiceInput);
  }
  
  // File upload simulation
  const fileBtn = document.getElementById('fileBtn');
  if (fileBtn) {
    fileBtn.addEventListener('click', simulateFileUpload);
  }
  
  // Quick actions
  const analyticsBtn = document.getElementById('analyticsBtn');
  if (analyticsBtn) {
    analyticsBtn.addEventListener('click', () => showRightSidebar('analytics'));
  }
  
  const settingsBtn = document.getElementById('settingsBtn');
  if (settingsBtn) {
    settingsBtn.addEventListener('click', () => showRightSidebar('settings'));
  }
  
  const workflowBtn = document.getElementById('workflowBtn');
  if (workflowBtn) {
    workflowBtn.addEventListener('click', () => showRightSidebar('workflows'));
  }
  
  console.log('Event listeners initialized');
}

// Switch Agent
function switchAgent(agentId) {
  console.log('Switching to agent:', agentId);
  currentAgent = agentId;
  
  // Update active agent in sidebar
  document.querySelectorAll('.agent-item').forEach(item => {
    item.classList.remove('active');
  });
  
  const activeAgent = document.querySelector(`[data-agent="${agentId}"]`);
  if (activeAgent) {
    activeAgent.classList.add('active');
  }
  
  // Update agent title
  const agent = appData.agents.find(a => a.id === agentId);
  const currentAgentTitle = document.getElementById('currentAgentTitle');
  if (agent && currentAgentTitle) {
    currentAgentTitle.textContent = agent.name + ' Agent';
  }
  
  // Load conversation
  loadConversation(agentId);
  
  // Update quick responses
  updateQuickResponses(agentId);
  
  // Close mobile sidebar
  if (window.innerWidth <= 768) {
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    if (sidebar) sidebar.classList.remove('active');
    if (mobileOverlay) mobileOverlay.classList.remove('active');
  }
  
  console.log('Agent switched to:', agentId);
}

// Load Conversation
function loadConversation(agentId) {
  const messages = appData.sampleConversations[agentId] || [];
  const chatMessages = document.getElementById('chatMessages');
  
  if (!chatMessages) {
    console.error('Chat messages element not found');
    return;
  }
  
  chatMessages.innerHTML = '';
  
  messages.forEach(message => {
    addMessageToChat(message);
  });
  
  // Scroll to bottom
  chatMessages.scrollTop = chatMessages.scrollHeight;
  
  console.log('Conversation loaded for agent:', agentId);
}

// Add Message to Chat
function addMessageToChat(message) {
  const chatMessages = document.getElementById('chatMessages');
  if (!chatMessages) {
    console.error('Chat messages element not found');
    return;
  }
  
  const messageElement = document.createElement('div');
  messageElement.className = `message ${message.type}`;
  
  const avatar = message.type === 'user' ? 'You' : '🤖';
  const time = new Date(message.timestamp || Date.now()).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
  
  let sentimentIndicator = '';
  if (message.sentiment) {
    sentimentIndicator = `<span class="sentiment-indicator">${message.sentiment}</span>`;
  }
  
  messageElement.innerHTML = `
    <div class="message-avatar">${avatar}</div>
    <div class="message-content">
      <div class="message-text">${message.message}</div>
      <div class="message-time">
        ${time}
        ${sentimentIndicator}
      </div>
    </div>
  `;
  
  chatMessages.appendChild(messageElement);
  chatMessages.scrollTop = chatMessages.scrollHeight;
  
  console.log('Message added to chat:', message.type);
}

// Send Message
function sendMessage() {
  const chatInput = document.getElementById('chatInput');
  const sendBtn = document.getElementById('sendBtn');
  
  if (!chatInput || !sendBtn) {
    console.error('Chat input elements not found');
    return;
  }
  
  const message = chatInput.value.trim();
  if (!message || isTyping) return;
  
  console.log('Sending message:', message);
  
  // Add user message
  addMessageToChat({
    type: 'user',
    message: message,
    timestamp: new Date().toISOString()
  });
  
  // Clear input
  chatInput.value = '';
  chatInput.style.height = 'auto';
  sendBtn.disabled = true;
  
  // Show typing indicator
  showTypingIndicator();
  
  // Simulate bot response
  setTimeout(() => {
    hideTypingIndicator();
    sendBotResponse(message);
  }, 1500 + Math.random() * 1000);
}

// Send Bot Response
function sendBotResponse(userMessage) {
  const responses = getBotResponse(userMessage, currentAgent);
  
  addMessageToChat({
    type: 'bot',
    message: responses.message,
    timestamp: new Date().toISOString(),
    sentiment: responses.sentiment,
    confidence: responses.confidence
  });
  
  console.log('Bot response sent for agent:', currentAgent);
}

// Get Bot Response
function getBotResponse(userMessage, agentId) {
  const responseTemplates = {
    'customer-service': [
      {
        message: "I understand your concern. Let me help you resolve this issue quickly. Based on your inquiry, I can see several options available to assist you.",
        sentiment: "helpful",
        confidence: 0.92
      },
      {
        message: "Thank you for reaching out! I've analyzed your request and I'm here to provide you with the most accurate information and support.",
        sentiment: "positive",
        confidence: 0.88
      }
    ],
    'sales-marketing': [
      {
        message: "Great question! Based on our current data analytics, I can provide you with actionable insights that will help drive your business forward.",
        sentiment: "enthusiastic",
        confidence: 0.94
      },
      {
        message: "I've analyzed the latest market trends and performance metrics. Here's what I recommend for optimizing your campaigns and increasing ROI.",
        sentiment: "confident",
        confidence: 0.91
      }
    ],
    'hr-operations': [
      {
        message: "I'm here to help with your HR needs! I can guide you through our policies, procedures, and automated workflows to ensure everything runs smoothly.",
        sentiment: "supportive",
        confidence: 0.89
      },
      {
        message: "Let me assist you with that HR matter. I have access to all current policies and can help streamline the process for you.",
        sentiment: "helpful",
        confidence: 0.93
      }
    ],
    'it-support': [
      {
        message: "I'm running a diagnostic on your system now... I can see the issue and have several solutions available. Let me walk you through the most effective approach.",
        sentiment: "technical",
        confidence: 0.96
      },
      {
        message: "Technical issue detected! I'm analyzing the logs and system performance. I can provide both automated fixes and manual troubleshooting steps.",
        sentiment: "analytical",
        confidence: 0.92
      }
    ],
    'research-analytics': [
      {
        message: "Excellent research question! I've compiled data from multiple sources and can provide comprehensive analysis with actionable insights.",
        sentiment: "analytical",
        confidence: 0.95
      },
      {
        message: "I've processed the latest market data and trends. Here's what the research shows, along with predictive modeling for future outcomes.",
        sentiment: "informative",
        confidence: 0.93
      }
    ],
    'creative-writing': [
      {
        message: "I love creative challenges! I can help you craft compelling content that resonates with your audience and aligns with your brand voice.",
        sentiment: "creative",
        confidence: 0.90
      },
      {
        message: "Let's bring your ideas to life! I can create multiple content variations and optimize them for different platforms and audiences.",
        sentiment: "enthusiastic",
        confidence: 0.87
      }
    ],
    'code-assistant': [
      {
        message: "I'm analyzing your code now... I can see several optimization opportunities and will provide detailed recommendations for improvement.",
        sentiment: "technical",
        confidence: 0.94
      },
      {
        message: "Code review complete! I've identified potential improvements in performance, security, and maintainability. Here's my analysis:",
        sentiment: "analytical",
        confidence: 0.96
      }
    ],
    'predictive-ai': [
      {
        message: "🔮 Based on current patterns and data trends, I'm generating predictive insights that will help you stay ahead of potential challenges.",
        sentiment: "predictive",
        confidence: 0.91
      },
      {
        message: "I've detected emerging patterns in your data. Here are my predictions and recommended proactive actions to optimize outcomes.",
        sentiment: "insightful",
        confidence: 0.88
      }
    ]
  };
  
  const templates = responseTemplates[agentId] || responseTemplates['customer-service'];
  const randomIndex = Math.floor(Math.random() * templates.length);
  
  return templates[randomIndex];
}

// Update Quick Responses
function updateQuickResponses(agentId) {
  const quickResponseTemplates = {
    'customer-service': [
      "Check order status",
      "Return policy",
      "Contact support",
      "FAQ"
    ],
    'sales-marketing': [
      "Campaign performance",
      "Lead analysis",
      "ROI metrics",
      "A/B test results"
    ],
    'hr-operations': [
      "Company policies",
      "Leave request",
      "Performance review",
      "Onboarding help"
    ],
    'it-support': [
      "System status",
      "Password reset",
      "Software update",
      "Security scan"
    ],
    'research-analytics': [
      "Market trends",
      "Competitor analysis",
      "Data visualization",
      "Custom report"
    ],
    'creative-writing': [
      "Blog post",
      "Social media",
      "Email campaign",
      "Product copy"
    ],
    'code-assistant': [
      "Code review",
      "Bug fix",
      "Performance optimization",
      "Documentation"
    ],
    'predictive-ai': [
      "Trend forecast",
      "Risk analysis",
      "Workflow automation",
      "Predictive model"
    ]
  };
  
  const responses = quickResponseTemplates[agentId] || [];
  const quickResponses = document.getElementById('quickResponses');
  
  if (!quickResponses) {
    console.error('Quick responses element not found');
    return;
  }
  
  quickResponses.innerHTML = '';
  
  responses.forEach(response => {
    const button = document.createElement('button');
    button.className = 'quick-response-btn';
    button.textContent = response;
    button.addEventListener('click', () => {
      const chatInput = document.getElementById('chatInput');
      const sendBtn = document.getElementById('sendBtn');
      if (chatInput && sendBtn) {
        chatInput.value = response;
        chatInput.focus();
        sendBtn.disabled = false;
      }
    });
    quickResponses.appendChild(button);
  });
  
  console.log('Quick responses updated for agent:', agentId);
}

// Typing Indicator
function showTypingIndicator() {
  isTyping = true;
  const typingIndicator = document.getElementById('typingIndicator');
  if (typingIndicator) {
    typingIndicator.classList.remove('hidden');
  }
}

function hideTypingIndicator() {
  isTyping = false;
  const typingIndicator = document.getElementById('typingIndicator');
  if (typingIndicator) {
    typingIndicator.classList.add('hidden');
  }
}

// Theme Toggle
function toggleTheme() {
  darkMode = !darkMode;
  document.documentElement.setAttribute('data-color-scheme', darkMode ? 'dark' : 'light');
  
  // Update chart colors if chart exists
  if (window.performanceChart) {
    updateChartTheme();
  }
  
  console.log('Theme toggled to:', darkMode ? 'dark' : 'light');
}

// Sidebar Toggle
function toggleSidebar() {
  const sidebar = document.getElementById('sidebar');
  const mobileOverlay = document.getElementById('mobileOverlay');
  
  if (window.innerWidth <= 768) {
    if (sidebar) sidebar.classList.toggle('active');
    if (mobileOverlay) mobileOverlay.classList.toggle('active');
  } else {
    const dashboard = document.querySelector('.dashboard');
    if (dashboard) dashboard.classList.toggle('sidebar-collapsed');
  }
}

// Show Right Sidebar
function showRightSidebar(tab) {
  const rightSidebar = document.getElementById('rightSidebar');
  const mobileOverlay = document.getElementById('mobileOverlay');
  
  if (window.innerWidth <= 768) {
    if (rightSidebar) rightSidebar.classList.add('active');
    if (mobileOverlay) mobileOverlay.classList.add('active');
  }
  
  // Switch to the requested tab
  document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
  document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
  
  const tabBtn = document.querySelector(`[data-tab="${tab}"]`);
  const tabContent = document.getElementById(tab + 'Tab');
  
  if (tabBtn) tabBtn.classList.add('active');
  if (tabContent) tabContent.classList.add('active');
}

// Close Mobile Menus
function closeMobileMenus() {
  const sidebar = document.getElementById('sidebar');
  const rightSidebar = document.getElementById('rightSidebar');
  const mobileOverlay = document.getElementById('mobileOverlay');
  
  if (sidebar) sidebar.classList.remove('active');
  if (rightSidebar) rightSidebar.classList.remove('active');
  if (mobileOverlay) mobileOverlay.classList.remove('active');
}

// Simulate Voice Input
function simulateVoiceInput() {
  const voiceBtn = document.getElementById('voiceBtn');
  const chatInput = document.getElementById('chatInput');
  const sendBtn = document.getElementById('sendBtn');
  
  if (voiceBtn) voiceBtn.style.color = 'var(--color-primary)';
  
  // Simulate voice recording
  setTimeout(() => {
    if (voiceBtn) voiceBtn.style.color = '';
    if (chatInput) {
      chatInput.value = "Hello, I'm using voice input to ask about my account status";
      chatInput.focus();
    }
    if (sendBtn) sendBtn.disabled = false;
  }, 2000);
}

// Simulate File Upload
function simulateFileUpload() {
  const fileBtn = document.getElementById('fileBtn');
  
  if (fileBtn) fileBtn.style.color = 'var(--color-primary)';
  
  // Simulate file processing
  setTimeout(() => {
    if (fileBtn) fileBtn.style.color = '';
    
    addMessageToChat({
      type: 'user',
      message: "📎 Uploaded: document.pdf",
      timestamp: new Date().toISOString()
    });
    
    setTimeout(() => {
      addMessageToChat({
        type: 'bot',
        message: "I've successfully processed your document. The file contains 5 pages and I can help you with any questions about its contents.",
        timestamp: new Date().toISOString(),
        sentiment: "helpful",
        confidence: 0.94
      });
    }, 1000);
  }, 1500);
}

// Initialize Chart
function initializeChart() {
  const ctx = document.getElementById('performanceChart');
  if (!ctx) {
    console.error('Performance chart canvas not found');
    return;
  }
  
  const chartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [{
      label: 'Conversations',
      data: [120, 190, 300, 500, 200, 300, 450],
      borderColor: '#1FB8CD',
      backgroundColor: 'rgba(31, 184, 205, 0.1)',
      tension: 0.4
    }, {
      label: 'Resolved',
      data: [100, 180, 280, 480, 190, 290, 430],
      borderColor: '#FFC185',
      backgroundColor: 'rgba(255, 193, 133, 0.1)',
      tension: 0.4
    }]
  };
  
  window.performanceChart = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(94, 82, 64, 0.1)'
          }
        },
        x: {
          grid: {
            color: 'rgba(94, 82, 64, 0.1)'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        }
      }
    }
  });
  
  console.log('Chart initialized');
}

// Update Chart Theme
function updateChartTheme() {
  const gridColor = darkMode ? 'rgba(119, 124, 124, 0.2)' : 'rgba(94, 82, 64, 0.1)';
  
  window.performanceChart.options.scales.y.grid.color = gridColor;
  window.performanceChart.options.scales.x.grid.color = gridColor;
  window.performanceChart.update();
}

// Update Metrics
function updateMetrics() {
  const activeConversations = document.getElementById('activeConversations');
  const resolvedToday = document.getElementById('resolvedToday');
  const avgResponseTime = document.getElementById('avgResponseTime');
  const satisfactionScore = document.getElementById('satisfactionScore');
  
  if (activeConversations) activeConversations.textContent = appData.metrics.activeConversations.toLocaleString();
  if (resolvedToday) resolvedToday.textContent = appData.metrics.resolvedToday.toLocaleString();
  if (avgResponseTime) avgResponseTime.textContent = appData.metrics.avgResponseTime;
  if (satisfactionScore) satisfactionScore.textContent = appData.metrics.satisfactionScore + '⭐';
  
  // Simulate real-time updates
  setInterval(() => {
    // Randomly update metrics
    if (activeConversations) {
      const currentActive = parseInt(activeConversations.textContent.replace(/,/g, ''));
      const change = Math.floor(Math.random() * 10) - 5;
      activeConversations.textContent = Math.max(0, currentActive + change).toLocaleString();
    }
    
    if (resolvedToday) {
      const currentResolved = parseInt(resolvedToday.textContent.replace(/,/g, ''));
      const resolvedChange = Math.floor(Math.random() * 5);
      resolvedToday.textContent = (currentResolved + resolvedChange).toLocaleString();
    }
  }, 10000);
}

// Handle window resize
window.addEventListener('resize', function() {
  if (window.innerWidth > 768) {
    const sidebar = document.getElementById('sidebar');
    const rightSidebar = document.getElementById('rightSidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    if (sidebar) sidebar.classList.remove('active');
    if (rightSidebar) rightSidebar.classList.remove('active');
    if (mobileOverlay) mobileOverlay.classList.remove('active');
  }
});

// Auto-generate predictive insights
setInterval(() => {
  if (currentAgent === 'predictive-ai') {
    const insights = [
      "🔮 Detected unusual traffic pattern - 15% increase in mobile users",
      "⚡ System optimization opportunity identified in database queries",
      "📊 Customer satisfaction trending upward by 8% this week",
      "🎯 Recommended campaign adjustment based on behavioral analysis"
    ];
    
    const randomInsight = insights[Math.floor(Math.random() * insights.length)];
    
    addMessageToChat({
      type: 'bot',
      message: randomInsight,
      timestamp: new Date().toISOString(),
      sentiment: "predictive",
      confidence: 0.85 + Math.random() * 0.1
    });
  }
}, 30000); // Every 30 seconds for demo purposes