module AI
  module OpenAI
    class Client < AI::Base::Client
      DEFAULT_MODEL = "gpt-4-turbo-preview"
      EMBEDDING_MODEL = "text-embedding-3-small"
      
      config.base_url = "https://api.openai.com/v1"

      def chat_completion(messages:, model: nil, temperature: 0.7, max_tokens: nil, stream: false, **options)
        model ||= default_model
        formatted_messages = format_messages(messages)

        params = {
          model: model,
          messages: formatted_messages,
          temperature: temperature,
          stream: stream
        }.merge(options)
        
        params[:max_tokens] = max_tokens if max_tokens

        handle_rate_limit do
          log_request("chat_completion", params)
          
          response = @http_client.post("chat/completions") do |req|
            req.headers["Authorization"] = "Bearer #{api_key}"
            req.headers["Content-Type"] = "application/json"
            req.body = params
          end

          log_response("chat_completion", response.body)
          format_chat_response(response.body)
        end
      end

      def embeddings(text:, model: EMBEDDING_MODEL, **options)
        params = {
          model: model,
          input: text
        }.merge(options)

        handle_rate_limit do
          log_request("embeddings", params)
          
          response = @http_client.post("embeddings") do |req|
            req.headers["Authorization"] = "Bearer #{api_key}"
            req.headers["Content-Type"] = "application/json"
            req.body = params
          end

          log_response("embeddings", response.body)
          format_embedding_response(response.body)
        end
      end

      def models
        handle_rate_limit do
          response = @http_client.get("models") do |req|
            req.headers["Authorization"] = "Bearer #{api_key}"
          end

          response.body["data"].select { |m| m["id"].start_with?("gpt") }
        end
      end

      private

      def format_chat_response(response)
        choice = response.dig("choices", 0)
        message = choice.dig("message")
        
        {
          content: message["content"],
          role: message["role"],
          finish_reason: choice["finish_reason"],
          usage: extract_usage(response),
          raw: response
        }
      end

      def format_embedding_response(response)
        embedding = response.dig("data", 0, "embedding")
        
        {
          embedding: embedding,
          usage: extract_usage(response),
          raw: response
        }
      end

      def extract_usage(response)
        usage = response["usage"] || {}
        {
          prompt_tokens: usage["prompt_tokens"] || 0,
          completion_tokens: usage["completion_tokens"] || 0,
          total_tokens: usage["total_tokens"] || 0
        }
      end
    end
  end
end