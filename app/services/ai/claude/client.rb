module AI
  module Claude
    class Client < AI::Base::Client
      DEFAULT_MODEL = "claude-3-opus-20240229"
      
      config.base_url = "https://api.anthropic.com/v1"

      def chat_completion(messages:, model: nil, temperature: 0.7, max_tokens: 4096, stream: false, **options)
        model ||= default_model
        formatted_messages = format_claude_messages(messages)
        system_message = extract_system_message(messages)

        params = {
          model: model,
          messages: formatted_messages,
          max_tokens: max_tokens,
          temperature: temperature,
          stream: stream
        }.merge(options)
        
        params[:system] = system_message if system_message

        handle_rate_limit do
          log_request("messages", params)
          
          response = @http_client.post("messages") do |req|
            req.headers["x-api-key"] = api_key
            req.headers["anthropic-version"] = "2023-06-01"
            req.headers["Content-Type"] = "application/json"
            req.body = params
          end

          log_response("messages", response.body)
          format_chat_response(response.body)
        end
      end

      def embeddings(text:, **options)
        raise NotImplementedError, "<PERSON> does not currently support embeddings API"
      end

      def models
        # <PERSON> doesn't have a models endpoint, return known models
        [
          { id: "claude-3-opus-20240229", name: "<PERSON> 3 Opus" },
          { id: "claude-3-sonnet-20240229", name: "<PERSON> 3 Sonnet" },
          { id: "claude-3-haiku-20240307", name: "<PERSON> 3 Haiku" },
          { id: "claude-2.1", name: "<PERSON> 2.1" },
          { id: "claude-2.0", name: "<PERSON> 2.0" }
        ]
      end

      private

      def format_claude_messages(messages)
        formatted = format_messages(messages)
        # Remove system messages as Claude handles them separately
        formatted.reject { |msg| msg[:role] == "system" }
      end

      def extract_system_message(messages)
        system_messages = messages.select { |msg| 
          (msg.is_a?(Hash) && msg["role"] == "system") || 
          (msg.respond_to?(:role) && msg.role == "system")
        }
        
        return nil if system_messages.empty?
        
        system_messages.map { |msg| 
          msg.is_a?(Hash) ? msg["content"] : msg.content 
        }.join("\n\n")
      end

      def format_chat_response(response)
        {
          content: response.dig("content", 0, "text"),
          role: "assistant",
          finish_reason: response["stop_reason"],
          usage: extract_usage(response),
          raw: response
        }
      end

      def extract_usage(response)
        {
          prompt_tokens: response.dig("usage", "input_tokens") || 0,
          completion_tokens: response.dig("usage", "output_tokens") || 0,
          total_tokens: (response.dig("usage", "input_tokens") || 0) + (response.dig("usage", "output_tokens") || 0)
        }
      end
    end
  end
end