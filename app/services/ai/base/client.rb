module AI
  module Base
    class Client
      include ActiveSupport::Configurable

      config_accessor :api_key, :base_url, :model, :timeout, :max_retries

      attr_reader :tenant, :agent

      def initialize(tenant: nil, agent: nil)
        @tenant = tenant
        @agent = agent
        @http_client = build_http_client
      end

      def chat_completion(messages:, **options)
        raise NotImplementedError, "Subclasses must implement #chat_completion"
      end

      def embeddings(text:, **options)
        raise NotImplementedError, "Subclasses must implement #embeddings"
      end

      def models
        raise NotImplementedError, "Subclasses must implement #models"
      end

      protected

      def build_http_client
        Faraday.new(url: config.base_url) do |faraday|
          faraday.request :json
          faraday.response :json, content_type: /\bjson$/
          faraday.response :raise_error
          faraday.adapter Faraday.default_adapter
          faraday.options.timeout = config.timeout || 30
          faraday.options.open_timeout = 10
        end
      end

      def api_key
        return config.api_key if config.api_key.present?
        return agent.settings["api_key"] if agent&.settings&.dig("api_key").present?
        return tenant.settings.dig(provider_name, "api_key") if tenant&.settings&.dig(provider_name, "api_key").present?
        
        Rails.application.credentials.dig(:ai, provider_name.to_sym, :api_key)
      end

      def default_model
        return config.model if config.model.present?
        return agent.settings["model"] if agent&.settings&.dig("model").present?
        
        self.class::DEFAULT_MODEL
      end

      def provider_name
        self.class.name.demodulize.downcase
      end

      def handle_rate_limit(&block)
        retries = 0
        begin
          yield
        rescue Faraday::TooManyRequestsError => e
          retries += 1
          if retries <= (config.max_retries || 3)
            sleep_time = extract_retry_after(e) || (2 ** retries)
            Rails.logger.info "Rate limited by #{provider_name}, sleeping for #{sleep_time}s"
            sleep(sleep_time)
            retry
          else
            raise
          end
        end
      end

      def extract_retry_after(error)
        if error.response && error.response[:headers]
          retry_after = error.response[:headers]["retry-after"]
          retry_after.to_i if retry_after
        end
      end

      def format_messages(messages)
        messages.map do |msg|
          case msg
          when Hash
            msg
          when Message
            {
              role: msg.role,
              content: msg.content,
              name: msg.metadata&.dig("name")
            }.compact
          else
            { role: "user", content: msg.to_s }
          end
        end
      end

      def extract_usage(response)
        {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      end

      def log_request(method, params)
        Rails.logger.debug "[#{provider_name.upcase}] #{method} request: #{params.inspect}"
      end

      def log_response(method, response)
        Rails.logger.debug "[#{provider_name.upcase}] #{method} response: #{response.inspect}"
      end
    end
  end
end