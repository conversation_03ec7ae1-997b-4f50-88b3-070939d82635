# 🚀 Real-Time AI Chat Demo

Your AI-powered multi-tenant e-commerce chatbot is ready! Here's how to start using it:

## Prerequisites

1. Set up your environment variables:
```bash
export OPENAI_API_KEY="your-openai-key"
# Optional: Add other AI provider keys
export ANTHROPIC_API_KEY="your-anthropic-key"
```

## Start the Application

1. **Start the Rails server:**
```bash
rails server
```

2. **In another terminal, start the background job processor:**
```bash
rails solid_queue:start
```

3. **In another terminal, compile Tailwind CSS (if needed):**
```bash
rails tailwindcss:watch
```

## Access the Chat Interface

1. Open your browser to: http://localhost:3000
2. Sign up for a new account (or login if you already have one)
3. You'll be prompted to create a tenant (company)
4. Choose an AI agent to start chatting!

## Features You Can Test

### 🤖 Multi-Agent System
- **Emma** (Customer Service): Ask about orders, returns, or general help
- **Alex** (Sales): Get product recommendations
- **Sam** (Technical Support): Ask technical questions

### 💬 Real-Time Features
- Instant message delivery via ActionCable
- Typing indicators while AI is thinking
- Token usage tracking
- Message history

### 🎯 Example Questions to Try

**For Emma (Customer Service):**
- "I need help with my order #12345"
- "How do I return a product?"
- "What's your shipping policy?"

**For Alex (Sales):**
- "I'm looking for a laptop for programming"
- "What's on sale today?"
- "Can you recommend a gift for my mom?"

**For Sam (Technical Support):**
- "My API integration isn't working"
- "How do I debug a 500 error?"
- "Can you help me with webhook setup?"

## Architecture Highlights

- **Real-time**: ActionCable + Hotwire for instant updates
- **AI Integration**: Supports OpenAI, Anthropic, Google, DeepSeek, and more
- **Multi-tenant**: Each company has isolated data
- **Background Jobs**: Solid Queue for async AI processing
- **Responsive Design**: Tailwind CSS for beautiful UI

## Development Tips

### Watch the logs:
```bash
tail -f log/development.log
```

### Test different AI providers:
Edit an agent's `llm_provider` in the Rails console:
```ruby
Agent.find_by(name: "Emma").update!(llm_provider: "anthropic", llm_model: "claude-3-haiku-20240307")
```

### Create a new agent type:
```ruby
Agent.create!(
  tenant: Tenant.first,
  name: "Data Analyst",
  kind: "analytics",
  llm_provider: "openai",
  llm_model: "gpt-4o"
)
```

## Troubleshooting

1. **No AI responses?** Check your API keys are set correctly
2. **Styling issues?** Make sure Tailwind is compiled: `rails tailwindcss:build`
3. **WebSocket errors?** Ensure ActionCable is running (it starts with Rails server)

## Next Steps

- Implement e-commerce integrations (Shopify, WooCommerce)
- Add embeddable chat widget
- Create admin dashboard
- Implement RAG with knowledge base
- Add analytics dashboard

Enjoy your AI-powered chat platform! 🎉