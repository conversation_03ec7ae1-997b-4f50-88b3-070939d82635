<% content_for :page_title, "AI Agents" %>

<div class="space-y-6">
  <!-- Header with action -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-text-primary">AI Agents</h1>
      <p class="text-sm text-text-secondary mt-1">Manage your AI agents and their configurations</p>
    </div>
    <%= link_to new_agent_path, class: "btn btn-primary" do %>
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
      </svg>
      Create Agent
    <% end %>
  </div>

  <% if @agents.any? %>
    <!-- Agents Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @agents.each do |agent| %>
        <div class="card hover:shadow-lg transition-shadow">
          <div class="p-6">
            <!-- Agent Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center gap-3">
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                  <span class="text-lg font-bold text-primary-600 dark:text-primary-400">
                    <%= agent.name.split.map(&:first).join.upcase %>
                  </span>
                </div>
                <div>
                  <h3 class="font-semibold text-text-primary"><%= agent.name %></h3>
                  <span class="text-xs px-2 py-1 rounded-full <%= agent.status == 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400' %>">
                    <%= agent.status.humanize %>
                  </span>
                </div>
              </div>
              <div class="relative" data-controller="dropdown">
                <button class="p-2 hover:bg-surface-hover rounded-lg transition-colors" data-action="click->dropdown#toggle">
                  <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                <div class="hidden absolute right-0 mt-2 w-48 bg-surface border border-border rounded-lg shadow-lg py-1 z-10" data-dropdown-target="menu">
                  <%= link_to "Edit", edit_agent_path(agent), class: "block px-4 py-2 text-sm text-text-primary hover:bg-surface-hover" %>
                  <%= link_to "Duplicate", "#", class: "block px-4 py-2 text-sm text-text-primary hover:bg-surface-hover" %>
                  <hr class="my-1 border-border">
                  <%= link_to "Delete", agent_path(agent), method: :delete, data: { confirm: "Are you sure?" }, class: "block px-4 py-2 text-sm text-red-600 hover:bg-surface-hover" %>
                </div>
              </div>
            </div>

            <!-- Agent Description -->
            <p class="text-sm text-text-secondary mb-4">
              <%= agent.description || "No description provided" %>
            </p>

            <!-- Agent Stats -->
            <div class="grid grid-cols-3 gap-4 mb-4">
              <div class="text-center">
                <p class="text-2xl font-bold text-text-primary"><%= agent.conversations.count %></p>
                <p class="text-xs text-text-tertiary">Conversations</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-text-primary">
                  <%= agent.conversations.where(status: 'resolved').count %>
                </p>
                <p class="text-xs text-text-tertiary">Resolved</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-text-primary">4.8</p>
                <p class="text-xs text-text-tertiary">Rating</p>
              </div>
            </div>

            <!-- Agent Capabilities -->
            <div class="flex flex-wrap gap-2 mb-4">
              <% (agent.capabilities || []).first(3).each do |capability| %>
                <span class="text-xs px-2 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded">
                  <%= capability %>
                </span>
              <% end %>
              <% if (agent.capabilities || []).length > 3 %>
                <span class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 rounded">
                  +<%= agent.capabilities.length - 3 %> more
                </span>
              <% end %>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-border">
              <%= link_to "Open Chat", conversations_agent_path(agent), class: "text-sm text-primary hover:text-primary-600" %>
              <%= link_to "Configure", edit_agent_path(agent), class: "btn btn-secondary btn-sm" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <!-- Empty State -->
    <div class="card">
      <div class="p-12 text-center">
        <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-text-primary mb-2">No agents yet</h3>
        <p class="text-text-secondary mb-6">Create your first AI agent to start handling customer conversations.</p>
        <%= link_to new_agent_path, class: "btn btn-primary inline-flex items-center" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Create Your First Agent
        <% end %>
      </div>
    </div>
  <% end %>
</div>