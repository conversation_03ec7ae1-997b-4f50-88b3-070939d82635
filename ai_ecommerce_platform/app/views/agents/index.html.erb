<% content_for :title, "AI Agents" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">AI Agents</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your AI agents and their configurations</p>
        </div>
        <div class="flex items-center gap-3">
          <%= link_to new_agent_path, class: "px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Create Agent
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">

    <% if @agents.any? %>
      <!-- Agents Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @agents.each do |agent| %>
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
            <!-- Agent Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center gap-3">
                <div class="w-12 h-12 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center">
                  <span class="text-lg font-bold text-teal-600 dark:text-teal-400">
                    <%= agent.name.split.map(&:first).join.upcase %>
                  </span>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900 dark:text-white"><%= agent.name %></h3>
                  <span class="text-xs px-2 py-1 rounded-full <%= agent.status == 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' %>">
                    <%= agent.status.humanize %>
                  </span>
                </div>
              </div>
              <div class="relative" data-controller="dropdown">
                <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" data-action="click->dropdown#toggle">
                  <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                <div class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10" data-dropdown-target="menu">
                  <%= link_to "Edit", edit_agent_path(agent), class: "block px-4 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700" %>
                  <%= link_to "Duplicate", "#", class: "block px-4 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700" %>
                  <hr class="my-1 border-gray-200 dark:border-gray-700">
                  <%= link_to "Delete", agent_path(agent), method: :delete, data: { confirm: "Are you sure?" }, class: "block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700" %>
                </div>
              </div>
            </div>

            <!-- Agent Description -->
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
              <%= agent.description || "No description provided" %>
            </p>

            <!-- Agent Stats -->
            <div class="grid grid-cols-3 gap-4 mb-4">
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= agent.conversations.count %></p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Conversations</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">
                  <%= agent.conversations.where(status: 'resolved').count %>
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Resolved</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">4.8</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Rating</p>
              </div>
            </div>

            <!-- Agent Capabilities -->
            <div class="flex flex-wrap gap-2 mb-4">
              <% (agent.capabilities || []).first(3).each do |capability| %>
                <span class="text-xs px-2 py-1 bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 rounded">
                  <%= capability %>
                </span>
              <% end %>
              <% if (agent.capabilities || []).length > 3 %>
                <span class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
                  +<%= agent.capabilities.length - 3 %> more
                </span>
              <% end %>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
              <%= link_to "Open Chat", conversations_agent_path(agent), class: "text-sm text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300" %>
              <%= link_to "Configure", edit_agent_path(agent), class: "px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" %>
            </div>
          </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-12 border border-gray-200 dark:border-gray-700 text-center">
        <div class="w-16 h-16 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No agents yet</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">Create your first AI agent to start handling customer conversations.</p>
        <%= link_to new_agent_path, class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Create Your First Agent
        <% end %>
      </div>
    <% end %>
  </div>
</div>