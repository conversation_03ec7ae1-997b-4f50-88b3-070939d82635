<% content_for :title, "Create New Agent" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create New Agent</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure your AI agent's personality, behavior, and capabilities</p>
        </div>
        <div class="flex items-center gap-3">
          <%= link_to agents_path, class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to Agents
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">
    <div class="max-w-4xl mx-auto">
      <%= form_with(model: @agent, local: true) do |form| %>
        <% if @agent.errors.any? %>
          <div class="rounded-lg bg-red-50 dark:bg-red-900/30 p-4 mb-6 border border-red-200 dark:border-red-800">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@agent.errors.count, "error") %> with your submission
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @agent.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Basic Information Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Basic Information</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Configure your agent's basic details and appearance</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_field :name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "e.g., Emma - Sales Assistant" %>
            </div>

            <div>
              <%= form.label :kind, "Agent Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.select :kind, options_for_select(Agent::AGENT_KINDS.map { |k| [k.humanize, k] }), { prompt: 'Select agent type' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
            </div>

            <div class="md:col-span-2">
              <%= form.label :greeting_message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_area :greeting_message, rows: 3, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "Hello! I'm Emma, your sales assistant. How can I help you today?" %>
            </div>

            <div class="md:col-span-2">
              <%= form.label :avatar_url, "Avatar URL (optional)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_field :avatar_url, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "https://example.com/avatar.png" %>
            </div>
          </div>
        </div>

        <!-- Personality & Behavior Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Personality & Behavior</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Define how your agent communicates and behaves</p>
          </div>

          <div class="space-y-6">
            <div>
              <%= form.label :personality, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_area :personality, rows: 4, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "Friendly, professional, and knowledgeable about our products. Always eager to help and provides detailed explanations." %>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :response_style, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.select :response_style, [
                  ['Professional', 'professional'],
                  ['Friendly', 'friendly'],
                  ['Casual', 'casual'],
                  ['Technical', 'technical']
                ], { prompt: 'Select style' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
              </div>

              <div>
                <%= form.label :language, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.select :language, [
                  ['English', 'en'],
                  ['Spanish', 'es'],
                  ['French', 'fr'],
                  ['German', 'de'],
                  ['Italian', 'it'],
                  ['Auto-detect', 'auto']
                ], { selected: 'en' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Model Configuration Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI Model Configuration</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Configure the AI model settings and behavior parameters</p>
          </div>

          <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :model_provider, "LLM Provider", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.select :model_provider, [
                  ['OpenAI', 'openai'],
                  ['Anthropic', 'anthropic'],
                  ['Google', 'google'],
                  ['DeepSeek', 'deepseek'],
                  ['OpenRouter', 'openrouter'],
                  ['Ollama', 'ollama'],
                  ['AWS Bedrock', 'bedrock']
                ], { prompt: 'Select provider' }, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
              </div>

              <div>
                <%= form.label :model_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.text_field :model_name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "gpt-4" %>
              </div>

              <div>
                <%= form.label :temperature, "Temperature (0-2)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.number_field :temperature, step: 0.1, min: 0, max: 2, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", value: 0.7 %>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Lower values make responses more focused and deterministic</p>
              </div>

              <div>
                <%= form.label :max_tokens, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                <%= form.number_field :max_tokens, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", value: 1000 %>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Maximum length of responses</p>
              </div>
            </div>

            <div>
              <%= form.label :system_prompt, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
              <%= form.text_area :system_prompt, rows: 5, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none", placeholder: "You are a helpful sales assistant for an e-commerce store. You have deep knowledge about our products and can help customers find what they need..." %>
            </div>
          </div>
        </div>

        <!-- Specializations Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Specializations</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Define what this agent specializes in</p>
          </div>

          <div data-controller="nested-form">
            <div data-nested-form-target="items">
              <%= form.fields_for :agent_specializations do |spec_form| %>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                  <div>
                    <%= spec_form.label :name, "Specialization", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                    <%= spec_form.text_field :name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "e.g., Product recommendations" %>
                  </div>
                  <div>
                    <%= spec_form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
                    <%= spec_form.text_field :description, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent", placeholder: "Brief description" %>
                  </div>
                  <div class="flex items-end">
                    <button type="button" class="w-full px-3 py-2 text-sm font-medium text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
                      Remove
                    </button>
                  </div>
                </div>
              <% end %>
            </div>

            <button type="button" data-action="click->nested-form#add" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add Specialization
            </button>
          </div>
        </div>

        <!-- Test Agent Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Test Your Agent</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">Send a test message to see how your agent responds</p>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Test Message</label>
              <input type="text" id="test-message" class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" placeholder="Type a test message...">
            </div>

            <button type="button" id="test-agent" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.436L3 21l2.436-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
              </svg>
              Test Response
            </button>

            <div id="test-response" class="hidden">
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-teal-600 flex items-center justify-center">
                      <span class="text-white font-medium text-sm">AI</span>
                    </div>
                  </div>
                  <div class="ml-3 flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">Agent Response</p>
                    <p class="mt-1 text-sm text-gray-700 dark:text-gray-300" id="response-text"></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <%= link_to agents_path, class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
            Cancel
          <% end %>
          <%= form.submit "Create Agent", class: "px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const testButton = document.getElementById('test-agent');
    const testMessage = document.getElementById('test-message');
    const responseDiv = document.getElementById('test-response');
    const responseText = document.getElementById('response-text');

    if (testButton) {
      testButton.addEventListener('click', function() {
        const message = testMessage.value.trim();

        if (!message) {
          alert('Please enter a test message first.');
          return;
        }

        // Show loading state
        responseDiv.classList.remove('hidden');
        responseText.textContent = 'Generating response...';
        testButton.disabled = true;
        testButton.innerHTML = `
          <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Testing...
        `;

        // Simulate API call
        setTimeout(() => {
          responseText.textContent = "I'd be happy to help you with that! Based on your agent configuration, I can assist with product recommendations and answer questions about our inventory. What specific product are you looking for today?";

          // Reset button
          testButton.disabled = false;
          testButton.innerHTML = `
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.436L3 21l2.436-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
            </svg>
            Test Response
          `;
        }, 1500);
      });
    }
  });
</script>