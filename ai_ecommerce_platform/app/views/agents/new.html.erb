<div class="min-h-screen bg-gray-50 py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Create New Agent
        </h2>
      </div>
    </div>

    <div class="mt-6">
      <%= form_with(model: @agent, local: true) do |form| %>
        <% if @agent.errors.any? %>
          <div class="rounded-md bg-red-50 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were <%= pluralize(@agent.errors.count, "error") %> with your submission
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @agent.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-6 gap-6">
              <!-- Basic Information -->
              <div class="col-span-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_field :name, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "e.g., Emma - Sales Assistant" %>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :role, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :role, ['sales', 'support', 'general', 'technical', 'billing'], { prompt: 'Select a role' }, class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              </div>

              <div class="col-span-6">
                <%= form.label :greeting_message, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_area :greeting_message, rows: 2, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "Hello! I'm Emma, your sales assistant. How can I help you today?" %>
              </div>

              <div class="col-span-6 sm:col-span-4">
                <%= form.label :avatar_url, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_field :avatar_url, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "https://example.com/avatar.png" %>
              </div>

              <!-- Personality & Behavior -->
              <div class="col-span-6 mt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Personality & Behavior</h3>
              </div>

              <div class="col-span-6">
                <%= form.label :personality, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_area :personality, rows: 3, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "Friendly, professional, and knowledgeable about our products. Always eager to help and provides detailed explanations." %>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :response_style, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :response_style, ['concise', 'detailed', 'conversational', 'formal'], { prompt: 'Select style' }, class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :language, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :language, [['English', 'en'], ['Spanish', 'es'], ['French', 'fr'], ['German', 'de'], ['Italian', 'it']], {}, class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              </div>

              <!-- AI Model Configuration -->
              <div class="col-span-6 mt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">AI Model Configuration</h3>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :model_provider, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :model_provider, ['openai', 'anthropic', 'google'], { prompt: 'Select provider' }, class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :model_name, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_field :model_name, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "gpt-4" %>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :temperature, "Temperature (0-1)", class: "block text-sm font-medium text-gray-700" %>
                <%= form.number_field :temperature, step: 0.1, min: 0, max: 1, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", value: 0.7 %>
                <p class="mt-1 text-sm text-gray-500">Lower values make responses more focused and deterministic</p>
              </div>

              <div class="col-span-6 sm:col-span-3">
                <%= form.label :max_tokens, class: "block text-sm font-medium text-gray-700" %>
                <%= form.number_field :max_tokens, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", value: 500 %>
                <p class="mt-1 text-sm text-gray-500">Maximum length of responses</p>
              </div>

              <div class="col-span-6">
                <%= form.label :system_prompt, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_area :system_prompt, rows: 4, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "You are a helpful sales assistant for an e-commerce store. You have deep knowledge about our products and can help customers find what they need..." %>
              </div>

              <!-- Specializations -->
              <div class="col-span-6 mt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Specializations</h3>
                <p class="text-sm text-gray-500 mb-4">Define what this agent specializes in</p>
              </div>

              <div class="col-span-6" data-controller="nested-form">
                <div data-nested-form-target="items">
                  <%= form.fields_for :agent_specializations do |spec_form| %>
                    <div class="grid grid-cols-6 gap-4 mb-4 p-4 bg-gray-50 rounded-md">
                      <div class="col-span-3">
                        <%= spec_form.label :name, "Specialization", class: "block text-sm font-medium text-gray-700" %>
                        <%= spec_form.text_field :name, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "e.g., Product recommendations" %>
                      </div>
                      <div class="col-span-2">
                        <%= spec_form.label :description, class: "block text-sm font-medium text-gray-700" %>
                        <%= spec_form.text_field :description, class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", placeholder: "Brief description" %>
                      </div>
                      <div class="col-span-1 flex items-end">
                        <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                          Remove
                        </button>
                      </div>
                    </div>
                  <% end %>
                </div>
                
                <button type="button" data-action="click->nested-form#add" class="mt-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                  Add Specialization
                </button>
              </div>

              <!-- Test Agent -->
              <div class="col-span-6 mt-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Test Your Agent</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Message</label>
                    <input type="text" id="test-message" class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="Type a test message...">
                  </div>
                  <button type="button" id="test-agent" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Test Response
                  </button>
                  <div id="test-response" class="mt-4 hidden">
                    <div class="bg-white rounded-lg shadow p-4">
                      <div class="flex items-start">
                        <div class="flex-shrink-0">
                          <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center">
                            <span class="text-white font-medium">AI</span>
                          </div>
                        </div>
                        <div class="ml-3 flex-1">
                          <p class="text-sm font-medium text-gray-900">Agent Response</p>
                          <p class="mt-1 text-sm text-gray-700" id="response-text"></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
            <%= link_to "Cancel", agents_path, class: "inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <%= form.submit "Create Agent", class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  document.getElementById('test-agent')?.addEventListener('click', async function() {
    const testMessage = document.getElementById('test-message').value;
    const responseDiv = document.getElementById('test-response');
    const responseText = document.getElementById('response-text');
    
    if (!testMessage) return;
    
    responseDiv.classList.remove('hidden');
    responseText.textContent = 'Generating response...';
    
    // This would normally make an API call to test the agent
    setTimeout(() => {
      responseText.textContent = "I'd be happy to help you with that! Based on your agent configuration, I can assist with product recommendations and answer questions about our inventory. What specific product are you looking for today?";
    }, 1000);
  });
</script>