<% content_for :title, "#{@agent.name} - AI Dashboard" %>

<!-- Top Header -->
<header class="header">
  <div class="header-left">
    <h1 class="current-agent-title" id="currentAgentTitle"><%= @agent.name %></h1>
    <span class="status-indicator online">● Online</span>
  </div>
  
  <div class="header-right">
    <button class="theme-toggle" id="themeToggle" data-controller="theme" data-action="click->theme#toggle">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="5"/>
        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
      </svg>
    </button>
    
    <div class="user-menu">
      <button class="user-avatar">
        <div class="avatar-circle"><%= current_user.full_name.split.map(&:first).join.upcase %></div>
      </button>
    </div>
  </div>
</header>

<!-- Chat Interface -->
<div class="chat-container">
  <div class="chat-messages" id="chatMessages">
    <% if @active_conversation && @active_conversation.messages.any? %>
      <% @active_conversation.messages.order(:created_at).each do |message| %>
        <div class="message <%= message.sender_type == 'Customer' ? 'user' : 'bot' %>">
          <% if message.sender_type == 'Agent' %>
            <div class="message-header">
              <span class="message-sender"><%= @agent.name %></span>
              <span class="message-time"><%= message.created_at.strftime("%I:%M %p") %></span>
            </div>
          <% end %>
          <div class="message-content">
            <%= message.content %>
          </div>
          <% if message.sender_type == 'Customer' %>
            <div class="message-time"><%= message.created_at.strftime("%I:%M %p") %></div>
          <% end %>
        </div>
      <% end %>
    <% else %>
      <!-- Sample conversation for demo -->
      <div class="message bot">
        <div class="message-header">
          <span class="message-sender"><%= @agent.name %></span>
          <span class="message-time">11:25 PM</span>
        </div>
        <div class="message-content">
          Hello! I'm <%= @agent.name %>, your AI assistant. How can I help you today?
        </div>
      </div>
    <% end %>
  </div>
  
  <div class="chat-input-container">
    <div class="typing-indicator hidden" id="typingIndicator">
      <span>AI is typing</span>
      <div class="typing-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    
    <div class="chat-input-wrapper">
      <div class="input-actions">
        <button class="input-btn" id="voiceBtn" title="Voice Input">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3zM19 10v2a7 7 0 0 1-14 0v-2M12 19v4M8 23h8"/>
          </svg>
        </button>
        
        <button class="input-btn" id="fileBtn" title="Attach File">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
          </svg>
        </button>
      </div>
      
      <textarea class="chat-input" id="chatInput" placeholder="Type your message..." rows="1"></textarea>
      
      <button class="send-btn" id="sendBtn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
        </svg>
      </button>
    </div>
    
    <div class="quick-responses" id="quickResponses">
      <button class="quick-response-btn">How can you help me?</button>
      <button class="quick-response-btn">What are your capabilities?</button>
      <button class="quick-response-btn">Show me an example</button>
      <button class="quick-response-btn">Get started</button>
    </div>
  </div>
</div>

<% content_for :right_sidebar do %>
  <div class="sidebar-tabs">
    <button class="tab-btn active" data-tab="analytics">Analytics</button>
    <button class="tab-btn" data-tab="workflows">Workflows</button>
    <button class="tab-btn" data-tab="settings">Settings</button>
  </div>
  
  <!-- Analytics Tab -->
  <div class="tab-content active" id="analyticsTab">
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-value" id="activeConversations"><%= @metrics[:active_conversations] %></div>
        <div class="metric-label">Active Conversations</div>
      </div>
      
      <div class="metric-card">
        <div class="metric-value" id="resolvedToday"><%= number_with_delimiter(@metrics[:resolved_today]) %></div>
        <div class="metric-label">Resolved Today</div>
      </div>
      
      <div class="metric-card">
        <div class="metric-value" id="avgResponseTime"><%= @metrics[:avg_response_time] %></div>
        <div class="metric-label">Avg Response Time</div>
      </div>
      
      <div class="metric-card">
        <div class="metric-value" id="satisfactionScore"><%= @metrics[:satisfaction_score] %>⭐</div>
        <div class="metric-label">Satisfaction Score</div>
      </div>
    </div>
    
    <div class="chart-container">
      <h3>Performance Trends</h3>
      <canvas id="performanceChart" width="400" height="200"></canvas>
    </div>
    
    <div class="insights-panel">
      <h3>AI Insights</h3>
      <div class="insight-item">
        <span class="insight-icon">🔮</span>
        <div class="insight-content">
          <p>Predicted 23% increase in support requests next week</p>
          <span class="insight-confidence">87% confidence</span>
        </div>
      </div>
      
      <div class="insight-item">
        <span class="insight-icon">📈</span>
        <div class="insight-content">
          <p>Customer satisfaction improved 12% this month</p>
          <span class="insight-confidence">95% confidence</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Workflows Tab -->
  <div class="tab-content" id="workflowsTab">
    <div class="workflow-item">
      <div class="workflow-header">
        <h4>Auto-Escalation</h4>
        <label class="toggle-switch">
          <input type="checkbox" checked>
          <span class="slider"></span>
        </label>
      </div>
      <p>Automatically escalate complex issues to human agents</p>
    </div>
    
    <div class="workflow-item">
      <div class="workflow-header">
        <h4>Smart Routing</h4>
        <label class="toggle-switch">
          <input type="checkbox" checked>
          <span class="slider"></span>
        </label>
      </div>
      <p>Route conversations to specialized agents based on context</p>
    </div>
    
    <div class="workflow-item">
      <div class="workflow-header">
        <h4>Predictive Responses</h4>
        <label class="toggle-switch">
          <input type="checkbox">
          <span class="slider"></span>
        </label>
      </div>
      <p>Generate proactive responses based on user behavior</p>
    </div>
  </div>
  
  <!-- Settings Tab -->
  <div class="tab-content" id="settingsTab">
    <div class="settings-section">
      <h4>General Settings</h4>
      
      <div class="setting-item">
        <label>Response Speed</label>
        <select class="form-control">
          <option>Fast (1-2s)</option>
          <option selected>Normal (2-3s)</option>
          <option>Slow (3-5s)</option>
        </select>
      </div>
      
      <div class="setting-item">
        <label>Language</label>
        <select class="form-control">
          <option selected>English</option>
          <option>Spanish</option>
          <option>French</option>
        </select>
      </div>
      
      <div class="setting-item">
        <label>Personality</label>
        <select class="form-control">
          <option>Professional</option>
          <option selected>Friendly</option>
          <option>Casual</option>
        </select>
      </div>
    </div>
    
    <div class="settings-section">
      <h4>Privacy & Security</h4>
      
      <div class="setting-item">
        <div class="setting-row">
          <span>Data Encryption</span>
          <label class="toggle-switch">
            <input type="checkbox" checked>
            <span class="slider"></span>
          </label>
        </div>
      </div>
      
      <div class="setting-item">
        <div class="setting-row">
          <span>Anonymous Mode</span>
          <label class="toggle-switch">
            <input type="checkbox">
            <span class="slider"></span>
          </label>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
  // Initialize performance chart
  document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceChart');
    if (ctx) {
      const performanceData = <%= @performance_data.to_json.html_safe %>;
      
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: performanceData.labels,
          datasets: [{
            label: 'Conversations',
            data: performanceData.conversations,
            borderColor: 'rgb(50, 184, 198)',
            backgroundColor: 'rgba(50, 184, 198, 0.1)',
            tension: 0.4,
            borderWidth: 2,
            pointRadius: 0,
            pointHoverRadius: 4
          }, {
            label: 'Resolved',
            data: performanceData.resolved,
            borderColor: 'rgb(168, 75, 47)',
            backgroundColor: 'rgba(168, 75, 47, 0.1)',
            tension: 0.4,
            borderWidth: 2,
            pointRadius: 0,
            pointHoverRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false,
                borderColor: 'rgba(119, 124, 124, 0.3)'
              },
              ticks: {
                color: 'rgba(167, 169, 169, 0.7)',
                font: {
                  size: 10
                }
              }
            },
            y: {
              grid: {
                color: 'rgba(119, 124, 124, 0.2)',
                borderColor: 'rgba(119, 124, 124, 0.3)'
              },
              ticks: {
                color: 'rgba(167, 169, 169, 0.7)',
                font: {
                  size: 10
                }
              }
            }
          }
        }
      });
    }

    // Chat functionality
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatMessages = document.getElementById('chatMessages');
    const typingIndicator = document.getElementById('typingIndicator');
    
    // Auto-resize textarea
    chatInput?.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = this.scrollHeight + 'px';
    });

    // Send message
    const sendMessage = () => {
      const message = chatInput.value.trim();
      if (message) {
        // Add user message to chat
        const userMessage = document.createElement('div');
        userMessage.className = 'message user';
        userMessage.innerHTML = `
          <div class="message-content">${message}</div>
          <div class="message-time">${new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}</div>
        `;
        chatMessages.appendChild(userMessage);
        
        // Clear input
        chatInput.value = '';
        chatInput.style.height = 'auto';
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Show typing indicator
        typingIndicator.classList.remove('hidden');
        
        // Simulate AI response
        setTimeout(() => {
          typingIndicator.classList.add('hidden');
          
          const botMessage = document.createElement('div');
          botMessage.className = 'message bot';
          botMessage.innerHTML = `
            <div class="message-header">
              <span class="message-sender"><%= @agent.name %></span>
              <span class="message-time">${new Date().toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}</span>
            </div>
            <div class="message-content">I'm processing your request. This is a demo response from the <%= @agent.name %> agent.</div>
          `;
          chatMessages.appendChild(botMessage);
          
          // Scroll to bottom
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 2000);
      }
    };

    sendBtn?.addEventListener('click', sendMessage);
    chatInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    // Quick responses
    document.querySelectorAll('.quick-response-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        chatInput.value = this.textContent;
        chatInput.focus();
      });
    });
  });
</script>