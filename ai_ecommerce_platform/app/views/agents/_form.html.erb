<%= form_with(model: agent, local: true) do |form| %>
  <% if agent.errors.any? %>
    <div class="rounded-lg bg-red-50 dark:bg-red-900/30 p-4 mb-6 border border-red-200 dark:border-red-800">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(agent.errors.count, "error") %> with your submission
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% agent.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Basic Information Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Basic Information</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Configure your agent's basic details and settings</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Name -->
      <div class="md:col-span-2">
        <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_field :name, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
      </div>

      <!-- Kind -->
      <div>
        <%= form.label :kind, "Agent Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.select :kind, options_for_select(Agent::AGENT_KINDS.map { |k| [k.humanize, k] }, agent.kind),
            { prompt: "Select agent type" },
            class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
      </div>

      <!-- Status -->
      <div>
        <%= form.label :status, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.select :status, options_for_select(Agent.statuses.map { |k,v| [k.humanize, k] }, agent.status),
            {},
            class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
      </div>

      <!-- Description -->
      <div class="md:col-span-2">
        <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <%= form.text_area :description, rows: 3, class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none" %>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Brief description of what this agent does.</p>
      </div>
    </div>
  </div>

  <!-- AI Configuration Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI Configuration</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Configure the AI model and behavior settings</p>
    </div>

    <div class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- LLM Provider -->
        <div>
          <%= form.label :llm_provider, "LLM Provider", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= form.select :llm_provider,
              options_for_select([
                ["OpenAI", "openai"],
                ["Anthropic", "anthropic"],
                ["Google", "google"],
                ["DeepSeek", "deepseek"],
                ["OpenRouter", "openrouter"],
                ["Ollama", "ollama"],
                ["AWS Bedrock", "bedrock"]
              ], agent.llm_provider),
              { prompt: "Select provider" },
              class: "w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent" %>
        </div>

        <!-- LLM Model -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Model</label>
          <input type="text" name="agent[settings][llm_model]" value="<%= agent.settings&.dig('llm_model') %>"
                 placeholder="e.g., gpt-4, claude-3"
                 class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        </div>
      </div>

      <!-- System Prompt -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Prompt</label>
        <textarea name="agent[settings][system_prompt]" rows="5"
                  class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"><%= agent.settings&.dig('system_prompt') %></textarea>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">The system prompt that defines the agent's behavior and personality.</p>
      </div>

      <!-- Greeting Message -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Greeting Message</label>
        <textarea name="agent[settings][greeting_message]" rows="3"
                  class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"><%= agent.settings&.dig('greeting_message') %></textarea>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">The initial message the agent sends when starting a conversation.</p>
      </div>
    </div>
  </div>

  <!-- Advanced Settings Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 mb-6">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Advanced Settings</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">Fine-tune the AI model parameters and response behavior</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Temperature</label>
        <input type="number" name="agent[settings][temperature]" value="<%= agent.settings&.dig('temperature') || 0.7 %>"
               min="0" max="2" step="0.1"
               class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Controls randomness (0 = deterministic, 2 = very creative)</p>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Tokens</label>
        <input type="number" name="agent[settings][max_tokens]" value="<%= agent.settings&.dig('max_tokens') || 1000 %>"
               min="1" max="4000"
               class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Maximum length of responses</p>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Response Language</label>
        <select name="agent[settings][language]"
                class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
          <option value="en" <%= 'selected' if agent.settings&.dig('language') == 'en' %>>English</option>
          <option value="es" <%= 'selected' if agent.settings&.dig('language') == 'es' %>>Spanish</option>
          <option value="fr" <%= 'selected' if agent.settings&.dig('language') == 'fr' %>>French</option>
          <option value="de" <%= 'selected' if agent.settings&.dig('language') == 'de' %>>German</option>
          <option value="auto" <%= 'selected' if agent.settings&.dig('language') == 'auto' %>>Auto-detect</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Response Style</label>
        <select name="agent[settings][response_style]"
                class="w-full px-3 py-2 text-sm text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
          <option value="professional" <%= 'selected' if agent.settings&.dig('response_style') == 'professional' %>>Professional</option>
          <option value="friendly" <%= 'selected' if agent.settings&.dig('response_style') == 'friendly' %>>Friendly</option>
          <option value="casual" <%= 'selected' if agent.settings&.dig('response_style') == 'casual' %>>Casual</option>
          <option value="technical" <%= 'selected' if agent.settings&.dig('response_style') == 'technical' %>>Technical</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex items-center justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
    <%= link_to "Cancel", agents_path, class: "px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
    <%= form.submit class: "px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" %>
  </div>
<% end %>