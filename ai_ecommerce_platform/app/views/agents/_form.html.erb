<%= form_with(model: agent, local: true) do |form| %>
  <% if agent.errors.any? %>
    <div class="rounded-md bg-red-50 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            There were <%= pluralize(agent.errors.count, "error") %> with your submission
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% agent.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div class="grid grid-cols-6 gap-6">
        <!-- Name -->
        <div class="col-span-6 sm:col-span-4">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_field :name, class: "mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
        </div>

        <!-- Kind -->
        <div class="col-span-6 sm:col-span-3">
          <%= form.label :kind, "Agent Type", class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :kind, options_for_select(Agent::AGENT_KINDS.map { |k| [k.humanize, k] }, agent.kind), 
              { prompt: "Select agent type" }, 
              class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm" %>
        </div>

        <!-- Status -->
        <div class="col-span-6 sm:col-span-3">
          <%= form.label :status, class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :status, options_for_select(Agent.statuses.map { |k,v| [k.humanize, k] }, agent.status), 
              {}, 
              class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm" %>
        </div>

        <!-- Description -->
        <div class="col-span-6">
          <%= form.label :description, class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_area :description, rows: 3, class: "mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
          <p class="mt-2 text-sm text-gray-500">Brief description of what this agent does.</p>
        </div>

        <!-- LLM Provider -->
        <div class="col-span-6 sm:col-span-3">
          <%= form.label :llm_provider, "LLM Provider", class: "block text-sm font-medium text-gray-700" %>
          <%= form.select :llm_provider, 
              options_for_select([
                ["OpenAI", "openai"],
                ["Anthropic", "anthropic"],
                ["Google", "google"],
                ["DeepSeek", "deepseek"],
                ["OpenRouter", "openrouter"],
                ["Ollama", "ollama"],
                ["AWS Bedrock", "bedrock"]
              ], agent.llm_provider), 
              { prompt: "Select provider" }, 
              class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm" %>
        </div>

        <!-- LLM Model -->
        <div class="col-span-6 sm:col-span-3">
          <label class="block text-sm font-medium text-gray-700">Model</label>
          <input type="text" name="agent[settings][llm_model]" value="<%= agent.settings&.dig('llm_model') %>" 
                 placeholder="e.g., gpt-4, claude-3" 
                 class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
        </div>

        <!-- System Prompt -->
        <div class="col-span-6">
          <label class="block text-sm font-medium text-gray-700">System Prompt</label>
          <textarea name="agent[settings][system_prompt]" rows="4" 
                    class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"><%= agent.settings&.dig('system_prompt') %></textarea>
          <p class="mt-2 text-sm text-gray-500">The system prompt that defines the agent's behavior and personality.</p>
        </div>

        <!-- Greeting Message -->
        <div class="col-span-6">
          <label class="block text-sm font-medium text-gray-700">Greeting Message</label>
          <textarea name="agent[settings][greeting_message]" rows="2" 
                    class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"><%= agent.settings&.dig('greeting_message') %></textarea>
          <p class="mt-2 text-sm text-gray-500">The initial message the agent sends when starting a conversation.</p>
        </div>

        <!-- Settings in JSON -->
        <div class="col-span-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h3>
          <div class="grid grid-cols-6 gap-4">
            <div class="col-span-3">
              <label class="block text-sm font-medium text-gray-700">Temperature</label>
              <input type="number" name="agent[settings][temperature]" value="<%= agent.settings&.dig('temperature') || 0.7 %>" 
                     min="0" max="2" step="0.1" 
                     class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            </div>
            <div class="col-span-3">
              <label class="block text-sm font-medium text-gray-700">Max Tokens</label>
              <input type="number" name="agent[settings][max_tokens]" value="<%= agent.settings&.dig('max_tokens') || 1000 %>" 
                     min="1" max="4000" 
                     class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            </div>
            <div class="col-span-3">
              <label class="block text-sm font-medium text-gray-700">Response Language</label>
              <select name="agent[settings][language]" 
                      class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                <option value="en" <%= 'selected' if agent.settings&.dig('language') == 'en' %>>English</option>
                <option value="es" <%= 'selected' if agent.settings&.dig('language') == 'es' %>>Spanish</option>
                <option value="fr" <%= 'selected' if agent.settings&.dig('language') == 'fr' %>>French</option>
                <option value="de" <%= 'selected' if agent.settings&.dig('language') == 'de' %>>German</option>
                <option value="auto" <%= 'selected' if agent.settings&.dig('language') == 'auto' %>>Auto-detect</option>
              </select>
            </div>
            <div class="col-span-3">
              <label class="block text-sm font-medium text-gray-700">Response Style</label>
              <select name="agent[settings][response_style]" 
                      class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm">
                <option value="professional" <%= 'selected' if agent.settings&.dig('response_style') == 'professional' %>>Professional</option>
                <option value="friendly" <%= 'selected' if agent.settings&.dig('response_style') == 'friendly' %>>Friendly</option>
                <option value="casual" <%= 'selected' if agent.settings&.dig('response_style') == 'casual' %>>Casual</option>
                <option value="technical" <%= 'selected' if agent.settings&.dig('response_style') == 'technical' %>>Technical</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
      <%= link_to "Cancel", agents_path, class: "inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" %>
      <%= form.submit class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" %>
    </div>
  </div>
<% end %>