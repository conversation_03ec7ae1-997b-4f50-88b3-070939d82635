<!DOCTYPE html>
<html class="<%= cookies[:theme] == 'dark' ? 'dark' : '' %>">
  <head>
    <title><%= content_for(:title) || "AI Dashboard" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-background text-text-primary">
    <% if user_signed_in? %>
      <div class="min-h-screen flex flex-col">
        <!-- Top Navigation Bar - Full Width -->
        <%= render 'layouts/top_nav' %>
        
        <!-- Main Content Area with Sidebar -->
        <div class="flex flex-1">
          <%= render 'layouts/sidebar' %>
          
          <main class="flex-1 ml-56">
            <% if notice.present? %>
              <div class="mb-4 p-4 bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 rounded-lg">
                <%= notice %>
              </div>
            <% end %>
            <% if alert.present? %>
              <div class="mb-4 p-4 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 rounded-lg">
                <%= alert %>
              </div>
            <% end %>
            
            <%= yield %>
          </main>
        </div>
      </div>
    <% else %>
      <%= yield %>
    <% end %>
  </body>
</html>
