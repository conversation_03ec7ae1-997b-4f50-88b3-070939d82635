<!DOCTYPE html>
<html class="h-full">
  <head>
    <title><%= content_for?(:title) ? yield(:title) : "ChatFlow AI - Login" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="h-full">
    <div class="min-h-screen flex">
      <!-- Left Side - Form -->
      <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div class="mx-auto w-full max-w-sm lg:w-96">
          <!-- Logo -->
          <div class="mb-8">
            <%= link_to root_path, class: "flex items-center space-x-2" do %>
              <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">ChatFlow AI</span>
            <% end %>
          </div>
          
          <!-- Alerts -->
          <% if notice.present? %>
            <div class="mb-4 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg animate-fade-in">
              <%= notice %>
            </div>
          <% end %>
          
          <% if alert.present? %>
            <div class="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg animate-fade-in">
              <%= alert %>
            </div>
          <% end %>
          
          <%= yield %>
        </div>
      </div>
      
      <!-- Right Side - Image/Graphics -->
      <div class="hidden lg:block relative w-0 flex-1">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-600 via-pink-600 to-indigo-600">
          <!-- Animated Background Pattern -->
          <div class="absolute inset-0 bg-black opacity-10"></div>
          <div class="absolute inset-0">
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-300 rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float-slow"></div>
            <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-300 rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float-slower"></div>
            <div class="absolute top-1/2 right-1/3 w-80 h-80 bg-indigo-300 rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float"></div>
          </div>
          
          <!-- Content -->
          <div class="absolute inset-0 flex items-center justify-center p-12">
            <div class="max-w-lg text-center">
              <h2 class="text-4xl font-bold text-white mb-6 animate-fade-in-up">
                <%= content_for?(:hero_title) ? yield(:hero_title) : "Welcome to the Future of Customer Conversations" %>
              </h2>
              <p class="text-xl text-white/90 mb-8 animate-fade-in-up animation-delay-200">
                <%= content_for?(:hero_subtitle) ? yield(:hero_subtitle) : "Join thousands of businesses using AI to delight their customers" %>
              </p>
              
              <!-- Stats -->
              <div class="grid grid-cols-3 gap-8 animate-fade-in-up animation-delay-400">
                <div class="text-center">
                  <div class="text-3xl font-bold text-white">10K+</div>
                  <div class="text-sm text-white/70">Active Users</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-white">50M+</div>
                  <div class="text-sm text-white/70">Messages</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-white">99.9%</div>
                  <div class="text-sm text-white/70">Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <style>
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
      }
      
      @keyframes float-slow {
        0%, 100% { transform: translateY(0px) translateX(0px); }
        50% { transform: translateY(-30px) translateX(10px); }
      }
      
      @keyframes float-slower {
        0%, 100% { transform: translateY(0px) translateX(0px); }
        50% { transform: translateY(-40px) translateX(-10px); }
      }
      
      .animate-float { animation: float 6s ease-in-out infinite; }
      .animate-float-slow { animation: float-slow 8s ease-in-out infinite; }
      .animate-float-slower { animation: float-slower 10s ease-in-out infinite; }
      
      @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      .animate-fade-in {
        animation: fade-in 0.5s ease-out;
      }
      
      @keyframes fade-in-up {
        from { 
          opacity: 0; 
          transform: translateY(20px);
        }
        to { 
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .animate-fade-in-up {
        animation: fade-in-up 0.8s ease-out forwards;
      }
      
      .animation-delay-200 { animation-delay: 200ms; }
      .animation-delay-400 { animation-delay: 400ms; }
    </style>
  </body>
</html>