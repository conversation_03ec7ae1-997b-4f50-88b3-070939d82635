<!DOCTYPE html>
<html class="<%= cookies[:theme] == 'dark' ? 'dark' : '' %>" data-color-scheme="<%= cookies[:theme] || 'light' %>">
  <head>
    <title><%= content_for(:title) || "AI Dashboard" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <div class="dashboard">
      <!-- Left Sidebar for Agent Conversation -->
      <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <div class="logo-icon">🤖</div>
            <span class="logo-text">AI Platform</span>
          </div>
          <button class="sidebar-toggle" id="sidebarToggle">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12h18M3 6h18M3 18h18"/>
            </svg>
          </button>
        </div>

        <nav class="sidebar-nav">
          <div class="nav-section">
            <h3 class="nav-section-title">Agents</h3>
            <div class="agent-list">
              <% @agents.each do |agent| %>
                <%= link_to conversations_agent_path(agent), class: "agent-item #{'active' if agent.id == @agent.id}" do %>
                  <div class="agent-icon" style="background: <%= agent.kind == 'customer_service' ? '#32b8c6' : agent.kind == 'sales' ? '#a84b2f' : '#5e5240' %>;">
                    <%= agent.kind == 'customer_service' ? '💬' : agent.kind == 'sales' ? '💰' : '🔧' %>
                  </div>
                  <div class="agent-info">
                    <div class="agent-name"><%= agent.name %></div>
                    <div class="agent-description"><%= agent.description.present? ? truncate(agent.description, length: 40) : agent.kind.humanize %></div>
                  </div>
                  <div class="agent-status online"></div>
                <% end %>
              <% end %>
            </div>
          </div>

          <div class="nav-section">
            <h3 class="nav-section-title">Navigation</h3>
            <div class="nav-links">
              <%= link_to user_root_path, class: "nav-link" do %>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                Dashboard
              <% end %>

              <%= link_to agents_path, class: "nav-link" do %>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                Agents
              <% end %>

              <%= link_to analytics_dashboard_path, class: "nav-link" do %>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                Analytics
              <% end %>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main Content Area -->
      <main class="main-content">
        <%= yield %>
      </main>

      <!-- Right Sidebar - Advanced Features -->
      <aside class="right-sidebar" id="rightSidebar">
        <%= yield :right_sidebar %>
      </aside>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script>
      // Initialize dashboard interactions
      document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const dashboard = document.querySelector('.dashboard');
        
        if (sidebarToggle) {
          sidebarToggle.addEventListener('click', function() {
            dashboard.classList.toggle('sidebar-collapsed');
          });
        }

        // Tab switching
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Update active states
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(t => t.classList.remove('active'));
            
            this.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
          });
        });

        // Mobile menu handling
        const mobileOverlay = document.getElementById('mobileOverlay');
        
        // Mobile sidebar toggle
        if (window.innerWidth <= 768) {
          sidebarToggle?.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
          });
          
          mobileOverlay?.addEventListener('click', function() {
            sidebar.classList.remove('active');
            document.getElementById('rightSidebar').classList.remove('active');
            this.classList.remove('active');
          });
        }
      });
    </script>
  </body>
</html>