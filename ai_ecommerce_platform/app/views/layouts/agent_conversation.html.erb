<!DOCTYPE html>
<html class="<%= cookies[:theme] == 'dark' ? 'dark' : '' %>" data-color-scheme="<%= cookies[:theme] || 'light' %>">
  <head>
    <title><%= content_for(:title) || "AI Dashboard" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <div class="dashboard">
      <%= render 'layouts/sidebar' %>
      
      <!-- Main Content Area -->
      <main class="main-content">
        <%= yield %>
      </main>

      <!-- Right Sidebar - Advanced Features -->
      <aside class="right-sidebar" id="rightSidebar">
        <%= yield :right_sidebar %>
      </aside>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <script>
      // Initialize dashboard interactions
      document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const dashboard = document.querySelector('.dashboard');
        
        if (sidebarToggle) {
          sidebarToggle.addEventListener('click', function() {
            dashboard.classList.toggle('sidebar-collapsed');
          });
        }

        // Tab switching
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Update active states
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(t => t.classList.remove('active'));
            
            this.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
          });
        });

        // Mobile menu handling
        const mobileOverlay = document.getElementById('mobileOverlay');
        
        // Mobile sidebar toggle
        if (window.innerWidth <= 768) {
          sidebarToggle?.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
          });
          
          mobileOverlay?.addEventListener('click', function() {
            sidebar.classList.remove('active');
            document.getElementById('rightSidebar').classList.remove('active');
            this.classList.remove('active');
          });
        }
      });
    </script>
  </body>
</html>