<% content_for :title, @knowledge_source.name %>

<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @knowledge_source.name %></h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          <%= @knowledge_source.description %>
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <span class="status status--<%= @knowledge_source.active? ? 'success' : 'info' %>">
          <%= @knowledge_source.active? ? 'Active' : 'Inactive' %>
        </span>
        <%= link_to "Edit", edit_knowledge_source_path(@knowledge_source), class: "btn btn-secondary" %>
        <%= button_to "Delete", @knowledge_source, method: :delete, 
            data: { confirm: "Are you sure?" }, 
            class: "btn btn-secondary" %>
      </div>
    </div>
  </div>

  <!-- Info Cards -->
  <div class="grid gap-6 md:grid-cols-3 mb-8">
    <div class="card">
      <div class="card__body">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Source Type</h3>
        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
          <%= @knowledge_source.source_type&.humanize || 'Not set' %>
        </p>
      </div>
    </div>

    <div class="card">
      <div class="card__body">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Sync Frequency</h3>
        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
          <%= @knowledge_source.sync_frequency&.humanize || 'Manual' %>
        </p>
      </div>
    </div>

    <div class="card">
      <div class="card__body">
        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Documents</h3>
        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
          <%= @documents.count %>
        </p>
      </div>
    </div>
  </div>

  <!-- Documents Section -->
  <div class="card">
    <div class="card__body">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Documents</h2>
        <% if @knowledge_source.source_type == 'document' %>
          <%= button_to "Process Documents", process_document_knowledge_source_path(@knowledge_source), 
              method: :post, 
              class: "btn btn-primary btn-sm" %>
        <% end %>
      </div>

      <% if @documents.any? %>
        <div class="space-y-4">
          <% @documents.each do |document| %>
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-start justify-between">
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white"><%= document.title %></h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <%= truncate(document.content, length: 150) %>
                  </p>
                  <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    Added <%= document.created_at.strftime("%B %d, %Y at %I:%M %p") %>
                  </div>
                </div>
                <div class="ml-4">
                  <span class="status status--<%= document.processed? ? 'success' : 'warning' %>">
                    <%= document.processed? ? 'Processed' : 'Pending' %>
                  </span>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <p class="text-gray-500 dark:text-gray-400">No documents in this knowledge source yet.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>