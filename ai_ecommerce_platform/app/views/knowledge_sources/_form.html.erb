<%= form_with(model: knowledge_source, local: true, class: "space-y-6") do |form| %>
  <% if knowledge_source.errors.any? %>
    <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(knowledge_source.errors.count, "error") %> with your submission
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% knowledge_source.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="card">
    <div class="card__body space-y-6">
      <div class="form-group">
        <%= form.label :name, class: "form-label" %>
        <%= form.text_field :name, class: "form-control", placeholder: "e.g., Product Documentation" %>
      </div>

      <div class="form-group">
        <%= form.label :description, class: "form-label" %>
        <%= form.text_area :description, class: "form-control", rows: 3, placeholder: "Describe what this knowledge source contains..." %>
      </div>

      <div class="form-group">
        <%= form.label :source_type, class: "form-label" %>
        <%= form.select :source_type, 
            options_for_select([
              ['Document Upload', 'document'],
              ['Website URL', 'website'],
              ['API Endpoint', 'api'],
              ['Database', 'database']
            ], knowledge_source.source_type), 
            { prompt: 'Select source type...' }, 
            class: "form-control" %>
      </div>

      <div class="form-group">
        <%= form.label :source_url, class: "form-label" %>
        <%= form.text_field :source_url, class: "form-control", placeholder: "https://example.com/docs or leave blank for file upload" %>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          For document uploads, leave this blank. For websites or APIs, enter the URL.
        </p>
      </div>

      <div class="form-group">
        <%= form.label :sync_frequency, "Sync Frequency", class: "form-label" %>
        <%= form.select :sync_frequency, 
            options_for_select([
              ['Manual Only', 'manual'],
              ['Daily', 'daily'],
              ['Weekly', 'weekly'],
              ['Monthly', 'monthly']
            ], knowledge_source.sync_frequency || 'manual'), 
            {}, 
            class: "form-control" %>
      </div>

      <div class="form-group">
        <label class="flex items-center">
          <%= form.check_box :active, class: "rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" %>
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
        </label>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Active knowledge sources will be included in agent responses.
        </p>
      </div>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", knowledge_sources_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>