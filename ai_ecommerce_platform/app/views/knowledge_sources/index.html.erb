<% content_for :title, "Knowledge Sources" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Knowledge Sources</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your AI agents' knowledge base</p>
        </div>
        <div class="flex items-center gap-3">
          <%= link_to new_knowledge_source_path, class: "px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add Knowledge Source
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">

  <!-- Knowledge Sources Grid -->
  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    <% @knowledge_sources.each do |source| %>
      <div class="card hover:shadow-lg transition-shadow duration-200">
        <div class="card__body">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <%= link_to source.name, source, class: "hover:text-primary-600" %>
              </h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <%= source.description %>
              </p>
            </div>
            <span class="status status--<%= source.active? ? 'success' : 'info' %>">
              <%= source.active? ? 'Active' : 'Inactive' %>
            </span>
          </div>

          <div class="mt-4 flex items-center justify-between text-sm">
            <span class="text-gray-500 dark:text-gray-400">
              Type: <%= source.source_type&.humanize || 'Not set' %>
            </span>
            <span class="text-gray-500 dark:text-gray-400">
              <%= source.documents.count %> documents
            </span>
          </div>

          <div class="mt-4 flex space-x-2">
            <%= link_to "View", source, class: "btn btn-secondary btn-sm" %>
            <%= link_to "Edit", edit_knowledge_source_path(source), class: "btn btn-secondary btn-sm" %>
            <% if source.source_type == 'document' %>
              <%= button_to "Process", process_document_knowledge_source_path(source), 
                  method: :post, 
                  class: "btn btn-secondary btn-sm",
                  data: { confirm: "Process this knowledge source?" } %>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <% if @knowledge_sources.empty? %>
      <div class="col-span-full">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-12 border border-gray-200 dark:border-gray-700 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-lg font-semibold text-gray-900 dark:text-white">No knowledge sources</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new knowledge source.</p>
          <div class="mt-6">
            <%= link_to new_knowledge_source_path, class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add Knowledge Source
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  </div>
</div>