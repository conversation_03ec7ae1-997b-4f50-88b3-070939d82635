<% content_for :title, "Sign Up - ChatFlow AI" %>
<% content_for :hero_title, "Start Your AI Journey" %>
<% content_for :hero_subtitle, "Join thousands of businesses transforming their customer experience with AI" %>

<div class="animate-fade-in-up">
  <div>
    <h2 class="text-3xl font-bold text-gray-900">Create your account</h2>
    <p class="mt-2 text-sm text-gray-600">
      Already have an account?
      <%= link_to "Sign in", new_user_session_path, class: "font-medium text-purple-600 hover:text-purple-500 transition-colors" %>
    </p>
  </div>

  <div class="mt-8">
    <!-- Social Signup Options -->
    <div class="grid grid-cols-2 gap-3">
      <button type="button" class="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
        <svg class="w-5 h-5" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        <span class="ml-2">Google</span>
      </button>

      <button type="button" class="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"/>
        </svg>
        <span class="ml-2">GitHub</span>
      </button>
    </div>

    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">Or continue with email</span>
        </div>
      </div>
    </div>

    <!-- Multi-step Form -->
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { 
      class: "mt-6", 
      id: "signup-form",
      data: {
        turbo: false,
        controller: "registration-form",
        registration_form_target: "form",
        action: "submit->registration-form#handleSubmit"
      }
    }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      
      <!-- Progress Indicator -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="step-indicator active" data-step="1" data-registration-form-target="indicator">
              <div class="step-number">1</div>
              <span class="step-label">Account</span>
            </div>
            <div class="step-connector"></div>
          </div>
          <div class="flex items-center">
            <div class="step-indicator" data-step="2" data-registration-form-target="indicator">
              <div class="step-number">2</div>
              <span class="step-label">Company</span>
            </div>
            <div class="step-connector"></div>
          </div>
          <div class="flex items-center">
            <div class="step-indicator" data-step="3" data-registration-form-target="indicator">
              <div class="step-number">3</div>
              <span class="step-label">Complete</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 1: Account Information -->
      <div class="form-step active" data-step="1" data-registration-form-target="step">
        <div class="space-y-6">
          <div>
            <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1 relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
              </div>
              <%= f.email_field :email, 
                  autofocus: true, 
                  autocomplete: "email",
                  placeholder: "<EMAIL>",
                  class: "appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200" %>
            </div>
          </div>

          <div data-controller="password-strength">
            <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1 relative" data-controller="password-toggle">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <%= f.password_field :password, 
                  autocomplete: "new-password",
                  placeholder: "••••••••",
                  class: "appearance-none block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200",
                  data: { 
                    password_strength_target: "input",
                    password_toggle_target: "input",
                    action: "input->password-strength#checkStrength"
                  } %>
              <button type="button" 
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      data-action="click->password-toggle#toggle">
                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="showIcon">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="hideIcon">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"></path>
                </svg>
              </button>
            </div>
            <% if @minimum_password_length %>
              <p class="mt-2 text-sm text-gray-500">
                <em>(<%= @minimum_password_length %> characters minimum)</em>
              </p>
            <% end %>
            
            <!-- Password Strength Indicator -->
            <div class="mt-3">
              <div class="flex justify-between items-center mb-1">
                <span class="text-xs text-gray-500">Password strength</span>
                <span class="text-xs text-gray-500" data-password-strength-target="strengthText">Too short</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%" data-password-strength-target="strengthBar"></div>
              </div>
            </div>
          </div>

          <div>
            <%= f.label :password_confirmation, "Confirm password", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1 relative" data-controller="password-toggle">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <%= f.password_field :password_confirmation, 
                  autocomplete: "new-password",
                  placeholder: "••••••••",
                  class: "appearance-none block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200",
                  data: { password_toggle_target: "input" } %>
              <button type="button" 
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      data-action="click->password-toggle#toggle">
                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="showIcon">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-password-toggle-target="hideIcon">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div class="mt-8">
          <button type="button" 
                  class="w-full py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg"
                  data-action="click->registration-form#nextStep">
            Continue
          </button>
        </div>
      </div>

      <!-- Step 2: Company Information -->
      <div class="form-step" data-step="2" style="display: none;" data-registration-form-target="step">
        <div class="space-y-6">
          <div>
            <label for="company_name" class="block text-sm font-medium text-gray-700">Company name</label>
            <div class="mt-1 relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <input type="text" 
                     name="company_name" 
                     id="company_name"
                     placeholder="Acme Corporation"
                     class="appearance-none block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200" />
            </div>
          </div>

          <div>
            <label for="company_size" class="block text-sm font-medium text-gray-700">Company size</label>
            <div class="mt-1">
              <select name="company_size" 
                      id="company_size"
                      class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200">
                <option value="">Select company size</option>
                <option value="1-10">1-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-500">201-500 employees</option>
                <option value="500+">500+ employees</option>
              </select>
            </div>
          </div>

          <div>
            <label for="industry" class="block text-sm font-medium text-gray-700">Industry</label>
            <div class="mt-1">
              <select name="industry" 
                      id="industry"
                      class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200">
                <option value="">Select your industry</option>
                <option value="ecommerce">E-commerce</option>
                <option value="saas">SaaS</option>
                <option value="finance">Finance</option>
                <option value="healthcare">Healthcare</option>
                <option value="education">Education</option>
                <option value="retail">Retail</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">What features are you most interested in?</label>
            <div class="space-y-3">
              <label class="flex items-start">
                <input type="checkbox" name="features[]" value="ai_chatbot" class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">AI-powered chatbot</span>
              </label>
              <label class="flex items-start">
                <input type="checkbox" name="features[]" value="analytics" class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">Advanced analytics</span>
              </label>
              <label class="flex items-start">
                <input type="checkbox" name="features[]" value="integrations" class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">E-commerce integrations</span>
              </label>
              <label class="flex items-start">
                <input type="checkbox" name="features[]" value="multi_agent" class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">Multi-agent system</span>
              </label>
            </div>
          </div>
        </div>

        <div class="mt-8 flex space-x-4">
          <button type="button" 
                  class="flex-1 py-3 px-4 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                  data-action="click->registration-form#prevStep">
            Back
          </button>
          <button type="button" 
                  class="flex-1 py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg"
                  data-action="click->registration-form#nextStep">
            Continue
          </button>
        </div>
      </div>

      <!-- Step 3: Terms & Complete -->
      <div class="form-step" data-step="3" style="display: none;" data-registration-form-target="step">
        <div class="space-y-6">
          <!-- Terms of Service -->
          <div class="bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Almost there!</h3>
            <div class="space-y-4">
              <label class="flex items-start">
                <input type="checkbox" name="terms" id="terms" required class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">
                  I agree to the <a href="#" class="text-purple-600 hover:text-purple-500">Terms of Service</a> and 
                  <a href="#" class="text-purple-600 hover:text-purple-500">Privacy Policy</a>
                </span>
              </label>
              
              <label class="flex items-start">
                <input type="checkbox" name="newsletter" class="mt-0.5 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">
                  Send me tips, product updates, and special offers (you can unsubscribe anytime)
                </span>
              </label>
            </div>
          </div>

          <!-- Benefits Summary -->
          <div class="bg-purple-50 rounded-lg p-6">
            <h4 class="text-sm font-semibold text-purple-900 mb-3">Your free trial includes:</h4>
            <ul class="space-y-2 text-sm text-purple-700">
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                14-day free trial, no credit card required
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Access to all features
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Free onboarding session
              </li>
              <li class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Cancel anytime
              </li>
            </ul>
          </div>
        </div>

        <div class="mt-8 flex space-x-4">
          <button type="button" 
                  class="flex-1 py-3 px-4 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                  data-action="click->registration-form#prevStep">
            Back
          </button>
          <%= f.submit "Create Account", class: "flex-1 py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-[1.02] hover:shadow-lg cursor-pointer" %>
        </div>
      </div>
    <% end %>

    <!-- Sign In Link -->
    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Already have an account? 
        <%= link_to "Sign in", new_user_session_path, class: "font-medium text-purple-600 hover:text-purple-500 transition-colors" %>
      </p>
    </div>
  </div>
</div>

<style>
  /* Step Indicator Styles */
  .step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }
  
  .step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .step-indicator.active .step-number,
  .step-indicator.completed .step-number {
    background: linear-gradient(to right, #9333ea, #ec4899);
    color: white;
  }
  
  .step-indicator.completed .step-number::after {
    content: "✓";
    position: absolute;
    font-size: 18px;
  }
  
  .step-indicator.completed .step-number {
    font-size: 0;
  }
  
  .step-label {
    margin-top: 8px;
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }
  
  .step-indicator.active .step-label,
  .step-indicator.completed .step-label {
    color: #9333ea;
  }
  
  .step-connector {
    height: 2px;
    background-color: #e5e7eb;
    flex: 1;
    margin: 0 -20px;
    position: relative;
    top: -20px;
  }
  
  .step-indicator.completed + .step-connector,
  .step-indicator.active + .step-connector {
    background: linear-gradient(to right, #9333ea, #ec4899);
  }
  
</style>

