<div class="min-h-screen bg-background flex items-center justify-center px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Logo -->
    <div class="text-center">
      <div class="mx-auto w-16 h-16 rounded-lg flex items-center justify-center" style="background-color: var(--color-primary);">
        <span class="font-bold text-2xl" style="color: var(--color-btn-primary-text);">AI</span>
      </div>
      <h2 class="mt-6 text-3xl font-extrabold text-text-primary">
        Welcome back
      </h2>
      <p class="mt-2 text-sm text-text-secondary">
        Or
        <%= link_to "create a new account", new_user_registration_path, class: "font-medium transition-colors" style="color: var(--color-primary);" %>
      </p>
    </div>

    <!-- Form Card -->
    <div class="card">
      <div class="p-8">
        <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6", data: { turbo: false } }) do |f| %>
          <!-- Email Field -->
          <div>
            <%= f.label :email, class: "block text-sm font-medium text-text-primary mb-2" %>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <%= f.email_field :email, 
                  autofocus: true, 
                  autocomplete: "email",
                  placeholder: "<EMAIL>",
                  class: "input-field pl-10" %>
            </div>
          </div>

          <!-- Password Field -->
          <div>
            <div class="flex items-center justify-between mb-2">
              <%= f.label :password, class: "block text-sm font-medium text-text-primary" %>
              <%= link_to "Forgot password?", new_password_path(resource_name), class: "text-sm transition-colors" style="color: var(--color-primary);" %>
            </div>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <%= f.password_field :password, 
                  autocomplete: "current-password",
                  placeholder: "••••••••",
                  class: "input-field pl-10 pr-10",
                  data: { password_target: "input" } %>
              <button type="button" 
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                      data-controller="password-toggle"
                      data-action="click->password-toggle#toggle">
                <svg class="h-5 w-5 text-text-tertiary hover:text-text-secondary transition-colors" 
                     fill="none" 
                     stroke="currentColor" 
                     viewBox="0 0 24 24"
                     data-password-toggle-target="icon">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- Remember Me -->
          <% if devise_mapping.rememberable? %>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "h-4 w-4 border-border rounded transition-all duration-200 focus:ring-2" style="accent-color: var(--color-primary);" %>
                <%= f.label :remember_me, "Remember me", class: "ml-2 block text-sm text-text-secondary" %>
              </div>
              
              <div class="text-sm">
                <%= link_to "Need help?", "#", class: "transition-colors" style="color: var(--color-primary);" %>
              </div>
            </div>
          <% end %>

          <!-- Submit Button -->
          <div>
            <%= f.submit "Sign in", class: "btn btn-primary w-full" %>
          </div>

          <!-- Divider -->
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-border"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-surface text-text-tertiary">Or continue with</span>
            </div>
          </div>

          <!-- Social Login -->
          <div class="grid grid-cols-2 gap-3">
            <button type="button" class="btn btn-secondary">
              <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Google
            </button>

            <button type="button" class="btn btn-secondary">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"/>
              </svg>
              GitHub
            </button>
          </div>
        <% end %>
      </div>

      <!-- Security Notice -->
      <div class="px-8 py-4 bg-surface-hover border-t border-border">
        <div class="flex items-start gap-3">
          <svg class="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
          <p class="text-sm text-text-secondary">
            Your connection is secure and encrypted. We never share your login credentials.
          </p>
        </div>
      </div>
    </div>

    <!-- Sign Up Link -->
    <div class="text-center">
      <p class="text-sm text-text-secondary">
        Don't have an account? 
        <%= link_to "Start your free trial", new_user_registration_path, class: "font-medium transition-colors" style="color: var(--color-primary);" %>
      </p>
    </div>
  </div>
</div>