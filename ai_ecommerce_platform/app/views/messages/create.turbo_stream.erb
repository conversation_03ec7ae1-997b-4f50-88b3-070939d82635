<%= turbo_stream.append "messages" do %>
  <%= render 'messages/message', message: @message %>
<% end %>

<%= turbo_stream.replace "message_form" do %>
  <%= form_with model: [@conversation, Message.new], 
                id: "message_form",
                data: { 
                  conversation_target: "form",
                  action: "turbo:submit-end->conversation#messageAdded"
                },
                class: "flex items-end space-x-4" do |f| %>
    <div class="flex-1">
      <%= f.text_area :content,
                      rows: 1,
                      placeholder: "Type your message...",
                      class: "w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-purple-500 resize-none",
                      data: { 
                        conversation_target: "input",
                        action: "keydown.enter->conversation#handleSubmit"
                      } %>
    </div>
    <%= f.submit "Send", 
                 class: "px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium cursor-pointer" %>
  <% end %>
<% end %>