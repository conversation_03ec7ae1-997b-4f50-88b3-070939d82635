<div class="flex <%= message.role == 'user' ? 'justify-end' : 'justify-start' %> mb-4">
  <div class="flex items-end space-x-2 <%= message.role == 'user' ? 'flex-row-reverse space-x-reverse' : '' %> max-w-xs lg:max-w-2xl">
    <% if message.role != 'user' %>
      <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
        <%= message.sender&.name&.first || 'AI' %>
      </div>
    <% end %>
    
    <div class="<%= message.role == 'user' ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-900' %> rounded-lg px-4 py-2">
      <p class="whitespace-pre-wrap break-words"><%= message.content %></p>
      <% if message.role != 'user' && message.metadata&.dig('usage', 'total_tokens') %>
        <p class="text-xs <%= message.role == 'user' ? 'text-purple-200' : 'text-gray-500' %> mt-1">
          <%= message.metadata.dig('usage', 'total_tokens') %> tokens
        </p>
      <% end %>
    </div>
    
    <% if message.role == 'user' %>
      <div class="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
        U
      </div>
    <% end %>
  </div>
</div>