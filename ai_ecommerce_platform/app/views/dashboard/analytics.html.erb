<div class="min-h-screen bg-gray-50">
  <div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        
        <!-- Date Range Selector -->
        <div class="flex items-center space-x-4">
          <select id="date-range" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
            <option value="24hours" <%= 'selected' if @date_range == '24hours' %>>Last 24 Hours</option>
            <option value="7days" <%= 'selected' if @date_range == '7days' %>>Last 7 Days</option>
            <option value="30days" <%= 'selected' if @date_range == '30days' %>>Last 30 Days</option>
            <option value="90days" <%= 'selected' if @date_range == '90days' %>>Last 90 Days</option>
          </select>
          
          <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export Report
          </button>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 mt-6">
      <!-- Overview Stats -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Conversations</dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900"><%= @metrics[:overview][:total_conversations] %></div>
                    <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                      <svg class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                      </svg>
                      <span class="sr-only">Increased by</span>
                      12%
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900"><%= @metrics[:overview][:avg_response_time] %> min</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Resolution Rate</dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900"><%= @metrics[:overview][:resolution_rate] %>%</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Row -->
      <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Conversation Trends -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Conversation Trends</h3>
            <div class="h-64">
              <canvas id="conversationTrends"></canvas>
            </div>
          </div>
        </div>

        <!-- Response Time Distribution -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Response Time Distribution</h3>
            <div class="h-64">
              <canvas id="responseTimeChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer Satisfaction & Resolution Stats -->
      <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Customer Satisfaction -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Customer Satisfaction</h3>
            <div class="space-y-4">
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Satisfied</span>
                  <span class="font-medium"><%= @metrics[:customer_satisfaction][:satisfied] %></span>
                </div>
                <div class="mt-1 relative">
                  <div class="overflow-hidden h-2 text-xs flex rounded bg-green-100">
                    <div style="width: <%= @metrics[:customer_satisfaction][:satisfaction_rate] %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
                  </div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Neutral</span>
                  <span class="font-medium"><%= @metrics[:customer_satisfaction][:neutral] %></span>
                </div>
                <div class="mt-1 relative">
                  <div class="overflow-hidden h-2 text-xs flex rounded bg-yellow-100">
                    <div style="width: <%= (@metrics[:customer_satisfaction][:neutral].to_f / (@metrics[:customer_satisfaction][:satisfied] + @metrics[:customer_satisfaction][:neutral] + @metrics[:customer_satisfaction][:dissatisfied]) * 100).round(1) %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-yellow-500"></div>
                  </div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Dissatisfied</span>
                  <span class="font-medium"><%= @metrics[:customer_satisfaction][:dissatisfied] %></span>
                </div>
                <div class="mt-1 relative">
                  <div class="overflow-hidden h-2 text-xs flex rounded bg-red-100">
                    <div style="width: <%= (@metrics[:customer_satisfaction][:dissatisfied].to_f / (@metrics[:customer_satisfaction][:satisfied] + @metrics[:customer_satisfaction][:neutral] + @metrics[:customer_satisfaction][:dissatisfied]) * 100).round(1) %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500"></div>
                  </div>
                </div>
              </div>
              
              <div class="pt-4 border-t border-gray-200">
                <div class="flex justify-between">
                  <span class="text-sm font-medium text-gray-900">Overall Satisfaction Rate</span>
                  <span class="text-lg font-semibold text-green-600"><%= @metrics[:customer_satisfaction][:satisfaction_rate] %>%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Resolution Statistics -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Resolution Statistics</h3>
            <div class="h-64">
              <canvas id="resolutionStats"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Agent Performance -->
      <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Agent Performance</h3>
            <div class="overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversations</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Response Time</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Satisfaction</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resolution Rate</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <% @metrics[:agent_performance].each do |agent| %>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <%= agent[:name] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:conversations_handled] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:avg_response_time] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <%= agent[:satisfaction_score] %>
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:resolution_rate] %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Topics -->
      <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Popular Topics</h3>
            <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-5">
              <% @metrics[:popular_topics].each do |topic| %>
                <div class="relative rounded-lg border border-gray-300 bg-white px-4 py-3 shadow-sm hover:border-gray-400">
                  <div class="text-sm font-medium text-gray-900"><%= topic[:topic] %></div>
                  <div class="text-xs text-gray-500 mt-1"><%= topic[:count] %> mentions</div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Conversation Trends Chart
  const trendsCtx = document.getElementById('conversationTrends').getContext('2d');
  const trendsData = <%= @metrics[:conversation_trends].to_json.html_safe %>;
  
  new Chart(trendsCtx, {
    type: 'line',
    data: trendsData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Response Time Distribution
  const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
  const responseTimeData = <%= @metrics[:response_times].to_json.html_safe %>;
  
  new Chart(responseTimeCtx, {
    type: 'doughnut',
    data: {
      labels: ['< 1 min', '1-5 min', '5-15 min', '> 15 min'],
      datasets: [{
        data: [
          responseTimeData.under_1_min,
          responseTimeData.under_5_min,
          responseTimeData.under_15_min,
          responseTimeData.over_15_min
        ],
        backgroundColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(251, 146, 60)',
          'rgb(239, 68, 68)'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });

  // Resolution Statistics
  const resolutionCtx = document.getElementById('resolutionStats').getContext('2d');
  const resolutionData = <%= @metrics[:resolution_stats].to_json.html_safe %>;
  
  new Chart(resolutionCtx, {
    type: 'bar',
    data: {
      labels: ['Resolved', 'Escalated', 'Abandoned', 'Active'],
      datasets: [{
        data: [
          resolutionData.resolved,
          resolutionData.escalated,
          resolutionData.abandoned,
          resolutionData.active
        ],
        backgroundColor: [
          'rgb(34, 197, 94)',
          'rgb(251, 146, 60)',
          'rgb(239, 68, 68)',
          'rgb(59, 130, 246)'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Date range selector
  document.getElementById('date-range').addEventListener('change', function() {
    window.location.href = '<%= analytics_dashboard_path %>?date_range=' + this.value;
  });
</script>