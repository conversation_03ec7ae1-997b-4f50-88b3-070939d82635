<% content_for :title, "Analytics Dashboard" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Analytics Dashboard</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Comprehensive analytics and performance insights</p>
        </div>
        <div class="flex items-center gap-3">
          <!-- Date Range Selector -->
          <select id="date-range" class="px-3 py-2 text-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
            <option value="24hours" <%= 'selected' if @date_range == '24hours' %>>Last 24 Hours</option>
            <option value="7days" <%= 'selected' if @date_range == '7days' %>>Last 7 Days</option>
            <option value="30days" <%= 'selected' if @date_range == '30days' %>>Last 30 Days</option>
            <option value="90days" <%= 'selected' if @date_range == '90days' %>>Last 90 Days</option>
          </select>

          <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Export Report
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">
    <!-- Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Total Conversations -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Conversations</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+12%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@metrics[:overview][:total_conversations]) %></span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">This period</span>
        </div>
      </div>

      <!-- Average Response Time -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg Response Time</h3>
          <span class="text-xs font-medium text-green-600 dark:text-green-400">-5%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= @metrics[:overview][:avg_response_time] %></span>
          <span class="text-lg text-gray-500 dark:text-gray-400 ml-1">min</span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">Faster than last period</span>
        </div>
      </div>

      <!-- Resolution Rate -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Resolution Rate</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+3%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= @metrics[:overview][:resolution_rate] %></span>
          <span class="text-lg text-gray-500 dark:text-gray-400 ml-1">%</span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">Issues resolved successfully</span>
        </div>
      </div>

      <!-- Unique Customers -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unique Customers</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+18%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@metrics[:overview][:unique_customers]) %></span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">Active this period</span>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Conversation Trends -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Conversation Trends</h3>
          <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
            View Details
          </button>
        </div>
        <div class="h-64">
          <canvas id="conversationTrends"></canvas>
        </div>
      </div>

      <!-- Response Time Distribution -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Response Time Distribution</h3>
          <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
            View Details
          </button>
        </div>
        <div class="h-64">
          <canvas id="responseTimeChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Customer Satisfaction & Resolution Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Customer Satisfaction -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Customer Satisfaction</h3>
          <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
            View Details
          </button>
        </div>
        <div class="space-y-4">
          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Satisfied</span>
              <span class="font-medium text-gray-900 dark:text-white"><%= @metrics[:customer_satisfaction][:satisfied] %></span>
            </div>
            <div class="mt-1 relative">
              <div class="overflow-hidden h-2 text-xs flex rounded bg-green-100 dark:bg-green-900/30">
                <div style="width: <%= @metrics[:customer_satisfaction][:satisfaction_rate] %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
              </div>
            </div>
          </div>

          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Neutral</span>
              <span class="font-medium text-gray-900 dark:text-white"><%= @metrics[:customer_satisfaction][:neutral] %></span>
            </div>
            <div class="mt-1 relative">
              <div class="overflow-hidden h-2 text-xs flex rounded bg-yellow-100 dark:bg-yellow-900/30">
                <div style="width: <%= (@metrics[:customer_satisfaction][:neutral].to_f / (@metrics[:customer_satisfaction][:satisfied] + @metrics[:customer_satisfaction][:neutral] + @metrics[:customer_satisfaction][:dissatisfied]) * 100).round(1) %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-yellow-500"></div>
              </div>
            </div>
          </div>

          <div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600 dark:text-gray-400">Dissatisfied</span>
              <span class="font-medium text-gray-900 dark:text-white"><%= @metrics[:customer_satisfaction][:dissatisfied] %></span>
            </div>
            <div class="mt-1 relative">
              <div class="overflow-hidden h-2 text-xs flex rounded bg-red-100 dark:bg-red-900/30">
                <div style="width: <%= (@metrics[:customer_satisfaction][:dissatisfied].to_f / (@metrics[:customer_satisfaction][:satisfied] + @metrics[:customer_satisfaction][:neutral] + @metrics[:customer_satisfaction][:dissatisfied]) * 100).round(1) %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500"></div>
              </div>
            </div>
          </div>

          <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between">
              <span class="text-sm font-medium text-gray-900 dark:text-white">Overall Satisfaction Rate</span>
              <span class="text-lg font-semibold text-green-600 dark:text-green-400"><%= @metrics[:customer_satisfaction][:satisfaction_rate] %>%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Resolution Statistics -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Resolution Statistics</h3>
          <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
            View Details
          </button>
        </div>
        <div class="h-64">
          <canvas id="resolutionStats"></canvas>
        </div>
      </div>
    </div>

    <!-- Agent Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Agent Performance</h3>
        <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
          View All Agents
        </button>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead>
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Agent</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Conversations</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg Response Time</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Satisfaction</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Resolution Rate</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <% @metrics[:agent_performance].each do |agent| %>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <%= agent[:name] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:conversations_handled] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:avg_response_time] %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <%= agent[:satisfaction_score] %>
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= agent[:resolution_rate] %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Topics -->
      <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Popular Topics</h3>
            <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-5">
              <% @metrics[:popular_topics].each do |topic| %>
                <div class="relative rounded-lg border border-gray-300 bg-white px-4 py-3 shadow-sm hover:border-gray-400">
                  <div class="text-sm font-medium text-gray-900"><%= topic[:topic] %></div>
                  <div class="text-xs text-gray-500 mt-1"><%= topic[:count] %> mentions</div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Conversation Trends Chart
  const trendsCtx = document.getElementById('conversationTrends').getContext('2d');
  const trendsData = <%= @metrics[:conversation_trends].to_json.html_safe %>;
  
  new Chart(trendsCtx, {
    type: 'line',
    data: trendsData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Response Time Distribution
  const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
  const responseTimeData = <%= @metrics[:response_times].to_json.html_safe %>;
  
  new Chart(responseTimeCtx, {
    type: 'doughnut',
    data: {
      labels: ['< 1 min', '1-5 min', '5-15 min', '> 15 min'],
      datasets: [{
        data: [
          responseTimeData.under_1_min,
          responseTimeData.under_5_min,
          responseTimeData.under_15_min,
          responseTimeData.over_15_min
        ],
        backgroundColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)',
          'rgb(251, 146, 60)',
          'rgb(239, 68, 68)'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });

  // Resolution Statistics
  const resolutionCtx = document.getElementById('resolutionStats').getContext('2d');
  const resolutionData = <%= @metrics[:resolution_stats].to_json.html_safe %>;
  
  new Chart(resolutionCtx, {
    type: 'bar',
    data: {
      labels: ['Resolved', 'Escalated', 'Abandoned', 'Active'],
      datasets: [{
        data: [
          resolutionData.resolved,
          resolutionData.escalated,
          resolutionData.abandoned,
          resolutionData.active
        ],
        backgroundColor: [
          'rgb(34, 197, 94)',
          'rgb(251, 146, 60)',
          'rgb(239, 68, 68)',
          'rgb(59, 130, 246)'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Date range selector
  document.getElementById('date-range').addEventListener('change', function() {
    window.location.href = '<%= analytics_dashboard_path %>?date_range=' + this.value;
  });
</script>