<% content_for :title, "Enterprise Dashboard" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Enterprise Dashboard</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Comprehensive overview of your AI agent ecosystem</p>
        </div>
        <div class="flex items-center gap-3">
          <button class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Export Data
            </span>
          </button>
          <button class="px-4 py-2 text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 rounded-lg transition-colors flex items-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Create Workflow
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">
    <!-- Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <!-- Active Agents -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Active Agents</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+8%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= current_tenant.agents.active.count %></span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">3 high-priority</span><br>
          <span>99.8% uptime</span>
        </div>
      </div>

      <!-- Today's Requests -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Today's Requests</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+12%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@conversations_today) %></span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">97.8% success rate</span><br>
          <span>1.05s avg response</span>
        </div>
      </div>

      <!-- Cost Today -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cost Today</h3>
          <span class="text-xs font-medium text-red-600 dark:text-red-400">-23%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white">$97.02</span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">$23.40 saved</span><br>
          <span>$2,156 projected monthly</span>
        </div>
      </div>

      <!-- Efficiency Gain -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Efficiency Gain</h3>
          <span class="text-xs font-medium text-teal-600 dark:text-teal-400">+18%</span>
        </div>
        <div class="flex items-baseline">
          <span class="text-3xl font-bold text-gray-900 dark:text-white">87.3%</span>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="text-gray-700 dark:text-gray-300">Target: 85%</span><br>
          <span>Best: 94.2%</span>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- Real-time Performance Chart -->
      <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Real-time Performance</h3>
          <button class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
            View Details
          </button>
        </div>
        <div class="h-64">
          <canvas id="performanceChart"></canvas>
        </div>
      </div>

      <!-- Agent Status -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Agent Status</h3>
        <div class="space-y-4">
          <% @top_agents.first(3).each do |agent| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-2 h-2 bg-teal-500 rounded-full animate-pulse"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white"><%= agent.name %></p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    <%= number_with_delimiter(agent.conversations.where(created_at: Date.today.all_day).count) %> requests · 
                    0.85s avg · 
                    99.8% uptime
                  </p>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Bottom Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Provider Usage -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Provider Usage</h3>
        <div class="space-y-3">
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">OpenAI GPT-4</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">45%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-teal-500 h-2 rounded-full" style="width: 45%"></div>
            </div>
          </div>
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">Claude 3</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">30%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-teal-500 h-2 rounded-full" style="width: 30%"></div>
            </div>
          </div>
          <div>
            <div class="flex items-center justify-between mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">Custom Models</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">25%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="bg-teal-500 h-2 rounded-full" style="width: 25%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Workflows -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Active Workflows</h3>
        <div class="space-y-3">
          <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Customer Onboarding</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">24 active instances</p>
              </div>
              <span class="px-2 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded">Active</span>
            </div>
          </div>
          <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Support Escalation</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">12 active instances</p>
              </div>
              <span class="px-2 py-1 text-xs font-medium text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 rounded">Active</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
        <div class="space-y-3">
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-green-500 rounded-full mt-1.5"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900 dark:text-white">Workflow completed successfully</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Customer onboarding - 2 min ago</p>
            </div>
          </div>
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900 dark:text-white">New agent deployed</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Sales Assistant v2.1 - 15 min ago</p>
            </div>
          </div>
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-yellow-500 rounded-full mt-1.5"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900 dark:text-white">High traffic detected</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Support queue - 1 hour ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    // Generate sample data points
    const labels = [];
    const data = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now - i * 60 * 60 * 1000);
      labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
      data.push(Math.floor(Math.random() * 40) + 60);
    }
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          borderColor: '#14b8a6',
          backgroundColor: 'rgba(20, 184, 166, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointRadius: 0,
          pointHoverRadius: 4,
          pointHoverBackgroundColor: '#14b8a6'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#14b8a6',
            borderWidth: 1,
            padding: 10,
            displayColors: false,
            callbacks: {
              label: function(context) {
                return context.parsed.y + ' requests/min';
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false,
              borderColor: 'rgba(156, 163, 175, 0.2)'
            },
            ticks: {
              color: '#9ca3af',
              maxTicksLimit: 8,
              font: {
                size: 11
              }
            }
          },
          y: {
            grid: {
              color: 'rgba(156, 163, 175, 0.1)',
              borderColor: 'rgba(156, 163, 175, 0.2)'
            },
            ticks: {
              color: '#9ca3af',
              font: {
                size: 11
              },
              callback: function(value) {
                return value;
              }
            },
            beginAtZero: true,
            max: 160
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    });
  });
</script>