<div class="min-h-screen bg-gray-50 py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Chat Widget Integration
        </h2>
      </div>
    </div>

    <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Installation Instructions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Installation Instructions</h3>
          
          <div class="space-y-4">
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-2">1. Copy your API key</h4>
              <div class="flex items-center space-x-2">
                <input 
                  type="text" 
                  value="<%= current_tenant.api_key %>" 
                  readonly
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-mono bg-gray-50"
                  id="api-key-input"
                />
                <button 
                  onclick="copyToClipboard('api-key-input')"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Copy
                </button>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-2">2. Add the widget script to your website</h4>
              <p class="text-sm text-gray-500 mb-2">
                Add the following code before the closing </body> tag on every page where you want the chat widget to appear:
              </p>
              <div class="relative">
                <pre class="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code id="widget-code">&lt;script 
  src="<%= request.base_url %>/widget/chat-widget.js"
  data-api-key="<%= current_tenant.api_key %>"
&gt;&lt;/script&gt;</code></pre>
                <button 
                  onclick="copyCode('widget-code')"
                  class="absolute top-2 right-2 px-2 py-1 text-xs text-gray-400 hover:text-white bg-gray-800 rounded"
                >
                  Copy
                </button>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-2">3. Optional: Pass customer information</h4>
              <p class="text-sm text-gray-500 mb-2">
                If you want to identify customers, add these data attributes:
              </p>
              <div class="relative">
                <pre class="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code id="widget-code-advanced">&lt;script 
  src="<%= request.base_url %>/widget/chat-widget.js"
  data-api-key="<%= current_tenant.api_key %>"
  data-customer-email="<EMAIL>"
  data-customer-name="John Doe"
&gt;&lt;/script&gt;</code></pre>
                <button 
                  onclick="copyCode('widget-code-advanced')"
                  class="absolute top-2 right-2 px-2 py-1 text-xs text-gray-400 hover:text-white bg-gray-800 rounded"
                >
                  Copy
                </button>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-2">4. Alternative: Initialize programmatically</h4>
              <p class="text-sm text-gray-500 mb-2">
                You can also initialize the widget using JavaScript:
              </p>
              <div class="relative">
                <pre class="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code id="widget-code-js">const chatWidget = new AIChatWidget({
  apiKey: '<%= current_tenant.api_key %>',
  customerEmail: '<EMAIL>',
  customerName: 'John Doe'
});</code></pre>
                <button 
                  onclick="copyCode('widget-code-js')"
                  class="absolute top-2 right-2 px-2 py-1 text-xs text-gray-400 hover:text-white bg-gray-800 rounded"
                >
                  Copy
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Widget Customization -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Widget Customization</h3>
          
          <%= form_with url: update_widget_settings_path, method: :patch, local: true do |form| %>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Position</label>
                <%= form.select :position, 
                  options_for_select([
                    ['Bottom Right', 'bottom-right'],
                    ['Bottom Left', 'bottom-left']
                  ], current_tenant.widget_config['position']),
                  {},
                  class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                %>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">Theme</label>
                <%= form.select :theme,
                  options_for_select([
                    ['Light', 'light'],
                    ['Dark', 'dark']
                  ], current_tenant.widget_config['theme']),
                  {},
                  class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                %>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">Primary Color</label>
                <div class="mt-1 flex items-center">
                  <%= form.color_field :primary_color,
                    value: current_tenant.widget_config['primary_color'] || '#0066CC',
                    class: "h-10 w-20"
                  %>
                  <span class="ml-3 text-sm text-gray-500">
                    <%= current_tenant.widget_config['primary_color'] || '#0066CC' %>
                  </span>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">Auto-open delay (seconds)</label>
                <%= form.number_field :auto_open_delay,
                  value: current_tenant.widget_config['auto_open_delay'],
                  placeholder: "Leave empty to disable",
                  class: "mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                %>
                <p class="mt-1 text-sm text-gray-500">
                  Automatically open the chat widget after this many seconds. Leave empty to disable.
                </p>
              </div>

              <div>
                <%= form.submit "Save Settings", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Live Preview -->
    <div class="mt-6 bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Live Preview</h3>
        <p class="text-sm text-gray-500 mb-4">
          This is how your chat widget will appear on your website:
        </p>
        
        <div class="bg-gray-100 rounded-lg p-8 relative" style="height: 600px;">
          <div class="absolute inset-0 flex items-center justify-center">
            <p class="text-gray-500">Your website content here</p>
          </div>
          
          <!-- Widget preview will be injected here -->
          <div id="widget-preview"></div>
        </div>
      </div>
    </div>

    <!-- Integration Status -->
    <div class="mt-6 bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Integration Status</h3>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">Widget Status</p>
              <p class="text-sm text-gray-500">Last checked: <%= 5.minutes.ago.strftime('%b %d, %Y at %I:%M %p') %></p>
            </div>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">Total Installations</p>
              <p class="text-sm text-gray-500">Websites using your widget</p>
            </div>
            <p class="text-2xl font-semibold text-gray-900">3</p>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">Widget Loads Today</p>
              <p class="text-sm text-gray-500">Times the widget was loaded</p>
            </div>
            <p class="text-2xl font-semibold text-gray-900">247</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    setTimeout(() => {
      button.textContent = originalText;
    }, 2000);
  }

  function copyCode(elementId) {
    const codeElement = document.getElementById(elementId);
    const textArea = document.createElement('textarea');
    textArea.value = codeElement.textContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    
    // Show feedback
    const button = codeElement.parentElement.querySelector('button');
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    setTimeout(() => {
      button.textContent = originalText;
    }, 2000);
  }

  // Load widget preview
  const script = document.createElement('script');
  script.src = '<%= request.base_url %>/widget/chat-widget.js';
  script.dataset.apiKey = '<%= current_tenant.api_key %>';
  document.getElementById('widget-preview').appendChild(script);
</script>