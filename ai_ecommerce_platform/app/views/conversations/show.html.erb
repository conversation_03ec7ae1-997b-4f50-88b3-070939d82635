<div class="flex flex-col h-screen bg-gray-50" data-controller="conversation" data-conversation-conversation-id-value="<%= @conversation.id %>">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <%= link_to conversations_path, class: "mr-4 text-gray-500 hover:text-gray-700" do %>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
          <% end %>
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
              <%= @agent.name.first %>
            </div>
            <div class="ml-3">
              <h2 class="text-lg font-semibold text-gray-900"><%= @agent.name %></h2>
              <p class="text-sm text-gray-600"><%= @agent.kind.humanize %></p>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= @agent.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
            <%= @agent.status.humanize %>
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="flex-1 overflow-hidden">
    <div class="max-w-4xl mx-auto h-full flex flex-col">
      <div class="flex-1 overflow-y-auto px-4 py-6" data-conversation-target="messages">
        <div id="messages">
          <% @messages.each do |message| %>
            <%= render 'messages/message', message: message %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Message Input -->
  <div class="bg-white border-t">
    <div class="max-w-4xl mx-auto px-4 py-4">
      <%= form_with model: [@conversation, Message.new], 
                    data: { 
                      conversation_target: "form",
                      action: "turbo:submit-end->conversation#messageAdded"
                    },
                    class: "flex items-end space-x-4" do |f| %>
        <div class="flex-1">
          <%= f.text_area :content,
                          rows: 1,
                          placeholder: "Type your message...",
                          class: "w-full px-4 py-2 border rounded-lg focus:outline-none focus:border-purple-500 resize-none",
                          data: { 
                            conversation_target: "input",
                            action: "keydown.enter->conversation#handleSubmit"
                          } %>
        </div>
        <%= f.submit "Send", 
                     class: "px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium cursor-pointer" %>
      <% end %>
    </div>
  </div>
</div>

<style>
  @keyframes bounce {
    0%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
  }
  
  .animate-bounce {
    animation: bounce 1.4s infinite;
  }
</style>