<% content_for :title, "AI Chat Assistant" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">AI Chat Assistant</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Choose an agent to start a conversation</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="p-6">

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      <% @agents.each do |agent| %>
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
          <div class="p-6">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg">
                <%= agent.name.first %>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900"><%= agent.name %></h3>
                <p class="text-sm text-gray-600"><%= agent.kind.humanize %></p>
              </div>
            </div>
            
            <p class="text-gray-600 mb-4">
              <% case agent.kind %>
              <% when 'customer_service' %>
                I can help with orders, returns, and general inquiries.
              <% when 'sales' %>
                I can assist with product recommendations and purchases.
              <% when 'technical_support' %>
                I can help troubleshoot technical issues and answer questions.
              <% else %>
                I'm here to assist you with various tasks.
              <% end %>
            </p>
            
            <div class="flex items-center justify-between">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= agent.active? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                <%= agent.status.humanize %>
              </span>
              
              <%= form_with url: conversations_path, method: :post, class: "inline" do |f| %>
                <%= f.hidden_field :agent_id, value: agent.id %>
                <%= f.submit "Start Chat", class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 cursor-pointer" %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <% if @conversations.any? %>
      <div>
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Recent Conversations</h2>
        <div class="bg-white rounded-lg shadow">
          <ul class="divide-y divide-gray-200">
            <% @conversations.each do |conversation| %>
              <li class="hover:bg-gray-50 transition-colors duration-150">
                <%= link_to conversation_path(conversation), class: "block px-6 py-4" do %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
                        <%= conversation.agent.name.first %>
                      </div>
                      <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900">
                          <%= conversation.agent.name %> - <%= conversation.agent.kind.humanize %>
                        </p>
                        <p class="text-sm text-gray-500">
                          <% if conversation.messages.any? %>
                            <%= truncate(conversation.messages.last.content, length: 50) %>
                          <% else %>
                            No messages yet
                          <% end %>
                        </p>
                      </div>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= time_ago_in_words(conversation.updated_at) %> ago
                    </div>
                  </div>
                <% end %>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>
  </div>
</div>
