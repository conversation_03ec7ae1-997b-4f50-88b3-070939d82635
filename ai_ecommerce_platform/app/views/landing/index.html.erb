<!-- Enhanced Hero Section with Interactive Elements -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
  <!-- Animated Gradient Background -->
  <div class="absolute inset-0 z-0">
    <div class="absolute inset-0 bg-gradient-to-br from-purple-900/90 via-pink-900/90 to-indigo-900/90 z-10"></div>
    <div class="absolute inset-0 bg-gradient-to-br from-purple-900 via-pink-900 to-indigo-900">
      <div class="absolute inset-0">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
      </div>
    </div>
  </div>
  
  <!-- Animated Particles -->
  <div class="absolute inset-0 z-20">
    <div class="particles">
      <% 30.times do |i| %>
        <div class="particle" style="--delay: <%= i * 0.3 %>s; --size: <%= rand(5..25) %>px; --x: <%= rand(-100..100) %>vw; --y: <%= rand(-100..100) %>vh;"></div>
      <% end %>
    </div>
  </div>

  <!-- Floating UI Elements -->
  <div class="absolute inset-0 z-20 overflow-hidden">
    <div class="floating-element" style="top: 20%; left: 10%; animation-delay: 0s;">
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
        <div class="text-white font-semibold">AI Response Time</div>
        <div class="text-2xl text-green-400">0.3s</div>
      </div>
    </div>
    <div class="floating-element" style="top: 60%; right: 15%; animation-delay: 2s;">
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
        <div class="text-white font-semibold">Customer Satisfaction</div>
        <div class="text-2xl text-yellow-400">98%</div>
      </div>
    </div>
    <div class="floating-element" style="bottom: 30%; left: 15%; animation-delay: 4s;">
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
        <div class="text-white font-semibold">Cost Reduction</div>
        <div class="text-2xl text-purple-400">-80%</div>
      </div>
    </div>
  </div>
  
  <!-- Hero Content -->
  <div class="relative z-30 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="animate-fade-in-up">
      <!-- Live Activity Badge -->
      <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-full text-white mb-8 border border-white/20 animate-pulse">
        <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-ping"></div>
        <span class="text-sm font-medium">🎉 <span id="live-activity">Sarah from TechCorp just started using ChatFlow AI</span></span>
      </div>
      
      <h1 class="text-6xl md:text-8xl font-bold mb-6 text-white">
        <span class="block">The Future of</span>
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 animate-gradient-x typewriter" data-words='["Customer Conversations", "AI Support", "Sales Automation", "Customer Success"]'>
          Customer Conversations
        </span>
      </h1>
      
      <p class="text-xl md:text-2xl text-gray-200 mb-12 max-w-4xl mx-auto leading-relaxed">
        Deploy AI agents that truly understand your customers. Boost sales by 300%, reduce support costs by 80%, and deliver experiences that turn visitors into loyal advocates.
      </p>
      
      <div class="flex flex-col sm:flex-row gap-6 justify-center mb-16">
        <%= link_to new_user_registration_path, class: "group relative inline-flex items-center px-8 py-4 overflow-hidden rounded-full bg-white text-gray-900 font-bold text-lg shadow-2xl transform hover:scale-105 transition-all duration-300" do %>
          <span class="relative z-10">Start Building Free</span>
          <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
          <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <% end %>
        
        <button class="group inline-flex items-center px-8 py-4 rounded-full bg-transparent text-white font-semibold text-lg border-2 border-white/30 backdrop-blur-sm hover:bg-white/10 hover:border-white/50 transition-all duration-300" onclick="openDemoVideo()">
          <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
          </svg>
          Watch 2-min Demo
        </button>
      </div>
      
      <!-- Animated Live Stats Dashboard -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
        <div class="stat-card group hover:scale-105 transition-transform cursor-pointer">
          <div class="text-4xl md:text-5xl font-bold text-white">
            <span class="counter" data-target="15000">0</span>+
          </div>
          <div class="text-gray-300 mt-2">Active AI Agents</div>
          <div class="text-xs text-green-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">↑ 23% this month</div>
        </div>
        <div class="stat-card group hover:scale-105 transition-transform cursor-pointer">
          <div class="text-4xl md:text-5xl font-bold text-white">
            <span class="counter" data-target="50">0</span>M+
          </div>
          <div class="text-gray-300 mt-2">Messages Processed</div>
          <div class="text-xs text-green-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">↑ 142% YoY</div>
        </div>
        <div class="stat-card group hover:scale-105 transition-transform cursor-pointer">
          <div class="text-4xl md:text-5xl font-bold text-white">
            <span class="counter" data-target="99.9" data-decimal="1">0</span>%
          </div>
          <div class="text-gray-300 mt-2">Uptime SLA</div>
          <div class="text-xs text-blue-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">Zero downtime in 90 days</div>
        </div>
        <div class="stat-card group hover:scale-105 transition-transform cursor-pointer">
          <div class="text-4xl md:text-5xl font-bold text-white">
            <span class="counter" data-target="4.9" data-decimal="1">0</span>
          </div>
          <div class="text-gray-300 mt-2">★ Customer Rating</div>
          <div class="text-xs text-yellow-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">From 10,000+ reviews</div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
    <div class="mouse-scroll">
      <div class="mouse">
        <div class="wheel"></div>
      </div>
    </div>
  </div>
</section>

<!-- Customer Logos Section -->
<section class="py-16 bg-gray-50 border-y border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <p class="text-center text-gray-600 mb-8 text-lg">Trusted by industry leaders worldwide</p>
    <div class="logos-carousel overflow-hidden">
      <div class="logos-track flex items-center space-x-16">
        <% %w[Shopify Amazon Microsoft Google Meta Stripe Salesforce HubSpot].each do |company| %>
          <div class="logo-item flex-shrink-0">
            <div class="h-12 w-32 bg-gray-300 rounded-lg flex items-center justify-center text-gray-600 font-bold">
              <%= company %>
            </div>
          </div>
        <% end %>
        <!-- Duplicate for seamless loop -->
        <% %w[Shopify Amazon Microsoft Google Meta Stripe Salesforce HubSpot].each do |company| %>
          <div class="logo-item flex-shrink-0">
            <div class="h-12 w-32 bg-gray-300 rounded-lg flex items-center justify-center text-gray-600 font-bold">
              <%= company %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>

<!-- Interactive Product Demo & Live Chat -->
<section class="py-24 bg-white relative overflow-hidden" id="demo">
  <div class="absolute inset-0 bg-gradient-to-br from-purple-50 via-transparent to-pink-50 opacity-50"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold mb-4 animate-pulse">
        LIVE DEMO
      </span>
      <h2 class="text-5xl font-bold text-gray-900 mb-6">Try It Yourself - Live!</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Chat with our AI right now. No signup required. See how it handles your questions in real-time.
      </p>
    </div>
    
    <div class="grid lg:grid-cols-2 gap-12 items-start">
      <!-- Interactive Live Chat -->
      <div class="order-2 lg:order-1">
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-200 transform hover:scale-[1.02] transition-all duration-300">
          <div class="bg-gradient-to-r from-purple-600 to-pink-600 p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center animate-pulse">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-white font-semibold">Live AI Assistant</h3>
                  <p class="text-purple-100 text-sm flex items-center">
                    <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                    Online - Average response time: 0.3s
                  </p>
                </div>
              </div>
              <button class="text-white/70 hover:text-white transition-colors" onclick="resetChat()">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <div class="h-96 overflow-y-auto bg-gray-50 p-4" id="chat-messages">
            <div class="text-center text-gray-500 py-8">
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              <p class="text-lg font-medium mb-2">Start a conversation!</p>
              <p class="text-sm">Ask about pricing, features, or how we can help your business.</p>
            </div>
          </div>
          
          <div class="border-t border-gray-200 p-4 bg-white">
            <form id="chat-form" class="flex space-x-2">
              <input 
                type="text" 
                id="chat-input"
                placeholder="Type your message..."
                class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                autocomplete="off"
              />
              <button 
                type="submit"
                class="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                Send
              </button>
            </form>
            <div class="mt-2 flex flex-wrap gap-2">
              <button onclick="sendQuickMessage('Tell me about pricing')" class="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                💰 Pricing
              </button>
              <button onclick="sendQuickMessage('What integrations do you support?')" class="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                🔌 Integrations
              </button>
              <button onclick="sendQuickMessage('How does the AI learn?')" class="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                🤖 AI Training
              </button>
              <button onclick="sendQuickMessage('Show me a demo')" class="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors">
                👀 Demo
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- ROI Calculator -->
      <div class="order-1 lg:order-2">
        <div class="bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-6">Calculate Your ROI</h3>
          
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Monthly support tickets
              </label>
              <input 
                type="range" 
                id="tickets-slider"
                min="100" 
                max="10000" 
                value="1000" 
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                oninput="updateROI()"
              />
              <div class="flex justify-between text-sm text-gray-600 mt-1">
                <span>100</span>
                <span id="tickets-value" class="font-bold text-purple-600">1,000</span>
                <span>10,000</span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Average handle time (minutes)
              </label>
              <input 
                type="range" 
                id="handle-time-slider"
                min="5" 
                max="60" 
                value="15" 
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                oninput="updateROI()"
              />
              <div class="flex justify-between text-sm text-gray-600 mt-1">
                <span>5 min</span>
                <span id="handle-time-value" class="font-bold text-purple-600">15 min</span>
                <span>60 min</span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Cost per agent hour
              </label>
              <input 
                type="range" 
                id="cost-slider"
                min="20" 
                max="100" 
                value="40" 
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                oninput="updateROI()"
              />
              <div class="flex justify-between text-sm text-gray-600 mt-1">
                <span>$20</span>
                <span id="cost-value" class="font-bold text-purple-600">$40</span>
                <span>$100</span>
              </div>
            </div>
          </div>
          
          <div class="mt-8 space-y-4 p-6 bg-white rounded-xl">
            <div class="text-center">
              <p class="text-sm text-gray-600 mb-2">Potential Monthly Savings</p>
              <p class="text-4xl font-bold text-green-600">$<span id="savings">8,000</span></p>
            </div>
            
            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div class="text-center">
                <p class="text-2xl font-bold text-purple-600"><span id="automation-rate">80</span>%</p>
                <p class="text-xs text-gray-600">Automation Rate</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-bold text-blue-600"><span id="hours-saved">200</span>h</p>
                <p class="text-xs text-gray-600">Hours Saved</p>
              </div>
            </div>
            
            <div class="pt-4">
              <p class="text-xs text-gray-500 text-center">
                Based on industry averages. Actual results may vary.
              </p>
            </div>
          </div>
          
          <button class="w-full mt-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            Get Detailed ROI Report →
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Advanced Features Grid -->
<section class="py-24 bg-gray-50 relative">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold mb-4">
        CAPABILITIES
      </span>
      <h2 class="text-5xl font-bold text-gray-900 mb-6">Everything You Need to Succeed</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Our platform combines cutting-edge AI with practical business tools to deliver results that matter
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <% [
        {
          title: "Omnichannel Support",
          description: "Connect with customers across web, mobile, WhatsApp, Facebook, Instagram, and more from one unified platform",
          icon: "omnichannel",
          color: "purple"
        },
        {
          title: "Smart Routing",
          description: "AI automatically routes conversations to the right agent based on intent, sentiment, and expertise",
          icon: "routing",
          color: "pink"
        },
        {
          title: "Revenue Intelligence",
          description: "Track conversion rates, identify upsell opportunities, and optimize every customer interaction",
          icon: "revenue",
          color: "indigo"
        },
        {
          title: "Sentiment Analysis",
          description: "Real-time emotion detection helps agents respond with empathy and escalate when needed",
          icon: "sentiment",
          color: "blue"
        },
        {
          title: "Workflow Automation",
          description: "Create custom workflows that trigger actions based on customer behavior and conversation patterns",
          icon: "workflow",
          color: "green"
        },
        {
          title: "Enterprise Security",
          description: "SOC 2 certified with end-to-end encryption, GDPR compliance, and role-based access control",
          icon: "security",
          color: "red"
        }
      ].each do |feature| %>
        <div class="group relative">
          <div class="absolute inset-0 bg-gradient-to-r <%= feature[:color] == 'purple' ? 'from-purple-600 to-purple-400' : feature[:color] == 'pink' ? 'from-pink-600 to-pink-400' : feature[:color] == 'indigo' ? 'from-indigo-600 to-indigo-400' : feature[:color] == 'blue' ? 'from-blue-600 to-blue-400' : feature[:color] == 'green' ? 'from-green-600 to-green-400' : 'from-red-600 to-red-400' %> rounded-2xl blur-xl opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
          <div class="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <!-- Feature Icons -->
            <div class="w-16 h-16 mb-6 <%= feature[:color] == 'purple' ? 'text-purple-600' : feature[:color] == 'pink' ? 'text-pink-600' : feature[:color] == 'indigo' ? 'text-indigo-600' : feature[:color] == 'blue' ? 'text-blue-600' : feature[:color] == 'green' ? 'text-green-600' : 'text-red-600' %>">
              <% case feature[:icon] %>
              <% when "omnichannel" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="8" y="8" width="20" height="20" rx="4" stroke="currentColor" stroke-width="2"/>
                  <rect x="36" y="8" width="20" height="20" rx="4" stroke="currentColor" stroke-width="2"/>
                  <rect x="8" y="36" width="20" height="20" rx="4" stroke="currentColor" stroke-width="2"/>
                  <rect x="36" y="36" width="20" height="20" rx="4" stroke="currentColor" stroke-width="2"/>
                  <path d="M18 28V36M46 28V36M28 18H36M28 46H36" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              <% when "routing" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="16" cy="16" r="8" stroke="currentColor" stroke-width="2"/>
                  <circle cx="48" cy="16" r="8" stroke="currentColor" stroke-width="2"/>
                  <circle cx="32" cy="48" r="8" stroke="currentColor" stroke-width="2"/>
                  <path d="M24 20L32 40M40 20L32 40" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              <% when "revenue" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 40L20 28L32 36L56 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M44 12H56V24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <rect x="8" y="48" width="8" height="8" fill="currentColor"/>
                  <rect x="28" y="44" width="8" height="12" fill="currentColor"/>
                  <rect x="48" y="36" width="8" height="20" fill="currentColor"/>
                </svg>
              <% when "sentiment" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="24" stroke="currentColor" stroke-width="2"/>
                  <circle cx="24" cy="28" r="3" fill="currentColor"/>
                  <circle cx="40" cy="28" r="3" fill="currentColor"/>
                  <path d="M20 40C20 40 24 44 32 44C40 44 44 40 44 40" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              <% when "workflow" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="8" y="8" width="16" height="16" rx="2" stroke="currentColor" stroke-width="2"/>
                  <rect x="40" y="8" width="16" height="16" rx="2" stroke="currentColor" stroke-width="2"/>
                  <rect x="24" y="40" width="16" height="16" rx="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M16 24V32H32V40M48 24V32H32V40" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              <% when "security" %>
                <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M32 8L12 18V34C12 46 20 54 32 56C44 54 52 46 52 34V18L32 8Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  <path d="M24 32L28 36L40 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              <% end %>
            </div>
            
            <h3 class="text-xl font-bold text-gray-900 mb-3">
              <%= feature[:title] %>
            </h3>
            <p class="text-gray-600 leading-relaxed">
              <%= feature[:description] %>
            </p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Integration Showcase -->
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-semibold mb-4">
        INTEGRATIONS
      </span>
      <h2 class="text-5xl font-bold text-gray-900 mb-6">Connects With Your Entire Stack</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Seamlessly integrate with 100+ tools you already use and love
      </p>
    </div>
    
    <div class="relative">
      <!-- Central Hub -->
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
        <div class="w-32 h-32 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-2xl">
          <span class="text-white font-bold text-xl">Your AI Hub</span>
        </div>
      </div>
      
      <!-- Integration Orbit -->
      <div class="relative h-96 max-w-4xl mx-auto">
        <% [
          { name: "Shopify", angle: 0 },
          { name: "Slack", angle: 45 },
          { name: "Salesforce", angle: 90 },
          { name: "HubSpot", angle: 135 },
          { name: "Stripe", angle: 180 },
          { name: "Zendesk", angle: 225 },
          { name: "WhatsApp", angle: 270 },
          { name: "Gmail", angle: 315 }
        ].each do |integration| %>
          <div class="integration-icon" style="--angle: <%= integration[:angle] %>deg;">
            <div class="w-20 h-20 bg-white rounded-xl shadow-lg flex items-center justify-center hover:scale-110 transition-transform cursor-pointer border border-gray-200">
              <span class="text-xs font-semibold text-gray-700"><%= integration[:name] %></span>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    
    <div class="text-center mt-16">
      <button class="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 font-semibold rounded-lg hover:bg-gray-200 transition-colors">
        View All Integrations
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>
</section>

<!-- Success Stories -->
<section class="py-24 bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold mb-4">
        SUCCESS STORIES
      </span>
      <h2 class="text-5xl font-bold text-gray-900 mb-6">Real Results From Real Businesses</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        See how companies like yours transformed their customer experience
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <% [
        {
          name: "TechStyle Fashion",
          logo: "TSF",
          quote: "We reduced response time by 95% and increased sales by 47% in just 3 months.",
          metrics: [
            { label: "Response Time", value: "-95%" },
            { label: "Sales Increase", value: "+47%" },
            { label: "Customer Satisfaction", value: "98%" }
          ],
          author: "Sarah Chen",
          role: "CEO",
          image: "SC"
        },
        {
          name: "CloudBase Inc",
          logo: "CBI",
          quote: "The AI handles 80% of support tickets automatically. Our team can focus on complex issues.",
          metrics: [
            { label: "Automation Rate", value: "80%" },
            { label: "Cost Reduction", value: "-65%" },
            { label: "Team Efficiency", value: "+300%" }
          ],
          author: "Marcus Rodriguez",
          role: "Head of Support",
          image: "MR"
        },
        {
          name: "GreenLeaf",
          logo: "GL",
          quote: "Integration was seamless. The AI learned our product catalog in hours, not weeks.",
          metrics: [
            { label: "Setup Time", value: "2 hours" },
            { label: "Conversion Rate", value: "+28%" },
            { label: "Cart Value", value: "+$45" }
          ],
          author: "Emily Watson",
          role: "E-commerce Director",
          image: "EW"
        }
      ].each do |story| %>
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300">
          <div class="p-8">
            <div class="flex items-center justify-between mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg flex items-center justify-center">
                <span class="text-purple-600 font-bold"><%= story[:logo] %></span>
              </div>
              <div class="flex text-yellow-400">
                <% 5.times do %>
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                <% end %>
              </div>
            </div>
            
            <h3 class="text-xl font-bold text-gray-900 mb-4"><%= story[:name] %></h3>
            <p class="text-gray-600 italic mb-6">"<%= story[:quote] %>"</p>
            
            <div class="space-y-3 mb-6">
              <% story[:metrics].each do |metric| %>
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600"><%= metric[:label] %></span>
                  <span class="text-sm font-bold text-purple-600"><%= metric[:value] %></span>
                </div>
              <% end %>
            </div>
            
            <div class="flex items-center pt-6 border-t border-gray-100">
              <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
                <%= story[:image] %>
              </div>
              <div class="ml-3">
                <div class="font-semibold text-gray-900"><%= story[:author] %></div>
                <div class="text-sm text-gray-600"><%= story[:role] %></div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-24 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold mb-4">
        FAQ
      </span>
      <h2 class="text-5xl font-bold text-gray-900 mb-6">Got Questions? We've Got Answers</h2>
    </div>
    
    <div class="space-y-4">
      <% [
        {
          question: "How quickly can I get started?",
          answer: "You can have your first AI agent live in under 10 minutes. Our platform guides you through setup with pre-built templates for your industry. No coding required!"
        },
        {
          question: "Will the AI understand my specific products and services?",
          answer: "Absolutely! Our AI learns from your product catalog, FAQs, and documentation. You can also train it with custom responses and it continuously improves from real conversations."
        },
        {
          question: "What happens if the AI can't answer a question?",
          answer: "Our smart handoff system seamlessly transfers complex queries to human agents. The AI provides full context, so your team can jump right in without asking customers to repeat themselves."
        },
        {
          question: "How secure is my customer data?",
          answer: "We're SOC 2 certified and use bank-level encryption. All data is stored in secure, GDPR-compliant data centers. You maintain full ownership and can export or delete data anytime."
        },
        {
          question: "Can I customize the AI's personality and tone?",
          answer: "Yes! You have complete control over how your AI communicates. Set the tone, personality, and even specific phrases to match your brand voice perfectly."
        }
      ].each_with_index do |faq, index| %>
        <div class="faq-item bg-gray-50 rounded-xl overflow-hidden">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-100 transition-colors" onclick="toggleFAQ(<%= index %>)">
            <span class="font-semibold text-gray-900"><%= faq[:question] %></span>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform faq-icon-<%= index %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer-<%= index %> hidden px-6 pb-4">
            <p class="text-gray-600"><%= faq[:answer] %></p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Enterprise CTA -->
<section class="py-24 bg-gradient-to-r from-purple-900 via-pink-900 to-indigo-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="stars"></div>
  </div>
  
  <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-full text-white mb-8 border border-white/20">
      <span class="text-sm font-medium">🎉 Limited Time: Get 3 months free on annual plans</span>
    </div>
    
    <h2 class="text-5xl md:text-6xl font-bold text-white mb-6">
      Ready to 10x Your Customer Experience?
    </h2>
    <p class="text-xl text-gray-200 mb-12 max-w-3xl mx-auto">
      Join 10,000+ businesses already using AI to delight customers and drive growth. Start free, scale as you grow.
    </p>
    
    <div class="flex flex-col sm:flex-row gap-6 justify-center">
      <%= link_to "Start Free Trial", new_user_registration_path, class: "group relative inline-flex items-center px-10 py-5 overflow-hidden rounded-full bg-white text-gray-900 font-bold text-lg shadow-2xl transform hover:scale-105 transition-all duration-300" %>
      
      <a href="#" class="inline-flex items-center px-10 py-5 rounded-full bg-transparent text-white font-semibold text-lg border-2 border-white/30 backdrop-blur-sm hover:bg-white/10 hover:border-white/50 transition-all duration-300">
        Schedule Enterprise Demo
      </a>
    </div>
    
    <p class="text-gray-300 mt-8">
      ✓ No credit card required &nbsp;&nbsp; ✓ 14-day free trial &nbsp;&nbsp; ✓ Cancel anytime
    </p>
  </div>
</section>

<!-- Premium CSS Animations -->
<style>
  /* Gradient Animation */
  @keyframes gradient-x {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  .animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient-x 4s ease infinite;
  }
  
  /* Particle Animation */
  .particle {
    position: absolute;
    width: var(--size);
    height: var(--size);
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
    border-radius: 50%;
    animation: float-particle 20s infinite;
    animation-delay: var(--delay);
    pointer-events: none;
  }
  
  @keyframes float-particle {
    0% { transform: translate(var(--x), 100vh) scale(0); opacity: 0; }
    10% { transform: translate(var(--x), 90vh) scale(1); opacity: 1; }
    90% { transform: translate(var(--x), -90vh) scale(1); opacity: 1; }
    100% { transform: translate(var(--x), -100vh) scale(0); opacity: 0; }
  }
  
  /* Floating UI Elements */
  .floating-element {
    position: absolute;
    animation: float-ui 6s ease-in-out infinite;
  }
  
  @keyframes float-ui {
    0%, 100% { transform: translateY(0) translateX(0) rotate(0deg); }
    25% { transform: translateY(-20px) translateX(10px) rotate(1deg); }
    50% { transform: translateY(10px) translateX(-5px) rotate(-1deg); }
    75% { transform: translateY(-10px) translateX(-10px) rotate(0.5deg); }
  }
  
  /* Mouse Scroll Animation */
  .mouse-scroll {
    animation: scroll-bounce 2s infinite;
  }
  
  .mouse {
    width: 30px;
    height: 50px;
    border: 2px solid rgba(255,255,255,0.8);
    border-radius: 15px;
    position: relative;
  }
  
  .wheel {
    width: 4px;
    height: 8px;
    background: rgba(255,255,255,0.8);
    border-radius: 2px;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    animation: wheel-scroll 2s infinite;
  }
  
  @keyframes scroll-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
  }
  
  @keyframes wheel-scroll {
    0% { transform: translateX(-50%) translateY(0); opacity: 1; }
    100% { transform: translateX(-50%) translateY(15px); opacity: 0; }
  }
  
  /* Fade In Up Animation */
  .animate-fade-in-up {
    animation: fade-in-up 1s ease-out;
  }
  
  @keyframes fade-in-up {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
  }
  
  /* Counter Animation */
  .counter {
    display: inline-block;
  }
  
  /* Logo Carousel */
  .logos-track {
    animation: scroll-logos 30s linear infinite;
  }
  
  @keyframes scroll-logos {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
  }
  
  /* Demo Chat Animation */
  .demo-message {
    animation: message-appear 0.5s ease-out forwards;
  }
  
  .demo-message.customer:nth-child(1) { animation-delay: 1s; }
  .demo-message.ai:nth-child(2) { animation-delay: 2s; }
  .demo-message.customer:nth-child(3) { animation-delay: 3s; }
  .demo-message.ai:nth-child(4) { animation-delay: 4s; }
  
  @keyframes message-appear {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
  }
  
  /* Integration Orbit */
  .integration-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateX(150px) rotate(calc(-1 * var(--angle)));
    animation: orbit 20s linear infinite;
  }
  
  @keyframes orbit {
    0% { transform: translate(-50%, -50%) rotate(var(--angle)) translateX(150px) rotate(calc(-1 * var(--angle))); }
    100% { transform: translate(-50%, -50%) rotate(calc(var(--angle) + 360deg)) translateX(150px) rotate(calc(-1 * var(--angle) - 360deg)); }
  }
  
  /* Stars Background */
  .stars {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, white, transparent),
      radial-gradient(2px 2px at 40px 70px, white, transparent),
      radial-gradient(1px 1px at 50px 160px, white, transparent),
      radial-gradient(1px 1px at 130px 40px, white, transparent);
    background-size: 200px 200px;
    animation: stars-move 100s linear infinite;
  }
  
  @keyframes stars-move {
    0% { transform: translateY(0); }
    100% { transform: translateY(-200px); }
  }
  
  /* Stat Cards */
  .stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  /* Typewriter Effect */
  .typewriter::after {
    content: '|';
    animation: blink 1s infinite;
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  /* Scroll Progress Bar */
  .scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(to right, #9333ea, #ec4899);
    z-index: 1000;
    transition: width 0.3s ease;
  }
  
  /* Sticky CTA */
  .sticky-cta {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999;
    transform: translateY(100px);
    transition: transform 0.3s ease;
  }
  
  .sticky-cta.show {
    transform: translateY(0);
  }
  
  /* Range Slider Styles */
  input[type="range"] {
    -webkit-appearance: none;
    background: transparent;
  }
  
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(to right, #9333ea, #ec4899);
    border-radius: 50%;
    cursor: pointer;
    margin-top: -8px;
  }
  
  input[type="range"]::-webkit-slider-track {
    width: 100%;
    height: 4px;
    cursor: pointer;
    background: #e5e7eb;
    border-radius: 2px;
  }
  
  /* Chat Message Animation */
  .chat-message-enter {
    animation: slide-in-up 0.3s ease-out;
  }
  
  @keyframes slide-in-up {
    0% { 
      opacity: 0; 
      transform: translateY(10px);
    }
    100% { 
      opacity: 1; 
      transform: translateY(0);
    }
  }
  
  /* Pulse Animation */
  @keyframes pulse-ring {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }
  
  .animate-pulse-ring::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    border-radius: inherit;
    animation: pulse-ring 2s ease-out infinite;
  }
</style>

<!-- JavaScript for Interactions -->
<script>
  // Counter Animation with decimal support
  document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter');
    const speed = 200;
    
    const animateCounter = (counter) => {
      const target = parseFloat(counter.getAttribute('data-target'));
      const decimal = parseInt(counter.getAttribute('data-decimal') || 0);
      const increment = target / speed;
      let current = 0;
      
      const updateCounter = () => {
        current += increment;
        if (current < target) {
          if (decimal > 0) {
            counter.textContent = current.toFixed(decimal);
          } else {
            counter.textContent = Math.ceil(current).toLocaleString();
          }
          requestAnimationFrame(updateCounter);
        } else {
          if (decimal > 0) {
            counter.textContent = target.toFixed(decimal);
          } else {
            counter.textContent = target.toLocaleString();
          }
        }
      };
      
      updateCounter();
    };
    
    // Intersection Observer for counters
    const observerOptions = {
      threshold: 0.5
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    counters.forEach(counter => {
      observer.observe(counter);
    });
    
    // Typewriter Effect
    const typewriters = document.querySelectorAll('.typewriter');
    typewriters.forEach(typewriter => {
      const words = JSON.parse(typewriter.getAttribute('data-words'));
      let wordIndex = 0;
      let charIndex = 0;
      let isDeleting = false;
      
      function type() {
        const currentWord = words[wordIndex];
        
        if (isDeleting) {
          typewriter.textContent = currentWord.substring(0, charIndex - 1);
          charIndex--;
        } else {
          typewriter.textContent = currentWord.substring(0, charIndex + 1);
          charIndex++;
        }
        
        if (!isDeleting && charIndex === currentWord.length) {
          isDeleting = true;
          setTimeout(type, 2000);
        } else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          wordIndex = (wordIndex + 1) % words.length;
          setTimeout(type, 500);
        } else {
          setTimeout(type, isDeleting ? 50 : 100);
        }
      }
      
      setTimeout(type, 1000);
    });
    
    // Live Activity Updates
    const activities = [
      "Sarah from TechCorp just started using ChatFlow AI",
      "Mike's team reduced response time by 95%",
      "Emma from StartupX achieved 98% customer satisfaction",
      "GlobalRetail processed their 1 millionth conversation",
      "John's AI agent resolved 500 tickets today",
      "TechStyle Fashion increased sales by 47%",
      "CloudBase automated 80% of support tickets",
      "New record: 0.2s average response time!"
    ];
    
    let activityIndex = 0;
    setInterval(() => {
      const activityElement = document.getElementById('live-activity');
      if (activityElement) {
        activityElement.style.opacity = '0';
        setTimeout(() => {
          activityIndex = (activityIndex + 1) % activities.length;
          activityElement.textContent = activities[activityIndex];
          activityElement.style.opacity = '1';
        }, 300);
      }
    }, 5000);
    
    // Scroll Progress Bar
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
      const windowHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = (window.scrollY / windowHeight) * 100;
      progressBar.style.width = scrolled + '%';
    });
    
    // Sticky CTA
    const stickyCTA = document.createElement('div');
    stickyCTA.className = 'sticky-cta';
    stickyCTA.innerHTML = `
      <a href="/users/sign_up" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
        <span>Start Free Trial</span>
        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
        </svg>
      </a>
    `;
    document.body.appendChild(stickyCTA);
    
    window.addEventListener('scroll', () => {
      if (window.scrollY > 500) {
        stickyCTA.classList.add('show');
      } else {
        stickyCTA.classList.remove('show');
      }
    });
  });
  
  // ROI Calculator
  function updateROI() {
    const tickets = document.getElementById('tickets-slider').value;
    const handleTime = document.getElementById('handle-time-slider').value;
    const costPerHour = document.getElementById('cost-slider').value;
    
    // Update display values
    document.getElementById('tickets-value').textContent = parseInt(tickets).toLocaleString();
    document.getElementById('handle-time-value').textContent = handleTime + ' min';
    document.getElementById('cost-value').textContent = '$' + costPerHour;
    
    // Calculate ROI
    const automationRate = 0.8; // 80% automation
    const totalHours = (tickets * handleTime) / 60;
    const hoursSaved = totalHours * automationRate;
    const savings = hoursSaved * costPerHour;
    
    // Update results
    document.getElementById('savings').textContent = Math.round(savings).toLocaleString();
    document.getElementById('automation-rate').textContent = Math.round(automationRate * 100);
    document.getElementById('hours-saved').textContent = Math.round(hoursSaved).toLocaleString();
  }
  
  // Interactive Chat Demo
  const chatResponses = {
    "tell me about pricing": "We offer flexible pricing starting at $99/month for small businesses. Our Professional plan at $299/month includes advanced features and priority support. Enterprise plans are customized to your needs. All plans include a 14-day free trial!",
    "what integrations do you support?": "We integrate with 100+ popular tools including Shopify, Salesforce, HubSpot, Slack, WhatsApp, Facebook Messenger, and more. Our API allows custom integrations with any platform you use.",
    "how does the ai learn?": "Our AI learns from your product catalog, FAQs, and past conversations. It continuously improves through machine learning, understanding context and customer intent better over time. You can also train it with custom responses.",
    "show me a demo": "I'd be happy to demonstrate our capabilities! I can help with order tracking, product recommendations, technical support, and more. What specific scenario would you like to see?",
    "default": "I'm here to help! Feel free to ask about our pricing, features, integrations, or how our AI can transform your customer experience."
  };
  
  function addChatMessage(message, isUser = true) {
    const chatContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'mb-4 chat-message-enter';
    
    if (isUser) {
      messageDiv.innerHTML = `
        <div class="flex justify-end">
          <div class="bg-purple-600 text-white rounded-2xl px-6 py-3 max-w-xs">
            ${message}
          </div>
        </div>
      `;
    } else {
      messageDiv.innerHTML = `
        <div class="flex justify-start items-end space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">
            AI
          </div>
          <div class="bg-gray-100 rounded-2xl px-6 py-3 max-w-lg">
            ${message}
          </div>
        </div>
      `;
    }
    
    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
  }
  
  function sendQuickMessage(message) {
    document.getElementById('chat-input').value = message;
    document.getElementById('chat-form').dispatchEvent(new Event('submit'));
  }
  
  function resetChat() {
    const chatContainer = document.getElementById('chat-messages');
    chatContainer.innerHTML = `
      <div class="text-center text-gray-500 py-8">
        <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <p class="text-lg font-medium mb-2">Conversation reset!</p>
        <p class="text-sm">Start fresh with a new question.</p>
      </div>
    `;
  }
  
  document.getElementById('chat-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (message) {
      // Clear welcome message if it exists
      const welcomeMessage = document.querySelector('#chat-messages > div:first-child');
      if (welcomeMessage && welcomeMessage.querySelector('svg')) {
        welcomeMessage.remove();
      }
      
      // Add user message
      addChatMessage(message, true);
      input.value = '';
      
      // Add AI response after delay
      setTimeout(() => {
        const lowerMessage = message.toLowerCase();
        let response = chatResponses.default;
        
        for (const key in chatResponses) {
          if (lowerMessage.includes(key)) {
            response = chatResponses[key];
            break;
          }
        }
        
        addChatMessage(response, false);
      }, 800);
    }
  });
  
  // FAQ Toggle
  function toggleFAQ(index) {
    const answer = document.querySelector(`.faq-answer-${index}`);
    const icon = document.querySelector(`.faq-icon-${index}`);
    
    answer.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }
  
  // Demo Video Modal
  function openDemoVideo() {
    alert('Demo video feature would open a modal with an embedded video player here!');
  }
  
  // Smooth Scroll
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
</script>