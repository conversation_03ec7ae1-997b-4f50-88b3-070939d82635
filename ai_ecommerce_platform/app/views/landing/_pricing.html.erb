<!-- Premium 3D Pricing Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 via-white to-purple-50 relative overflow-hidden">
  <!-- Background decoration -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>
  </div>

  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <span class="inline-block px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full text-sm font-semibold mb-4 animate-pulse">
        LIMITED TIME OFFER
      </span>
      <h2 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Pricing That Scales With You
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        Start free, upgrade as you grow. No hidden fees, no surprises.
      </p>
      
      <!-- Billing Toggle -->
      <div class="inline-flex items-center bg-gray-100 rounded-full p-1">
        <button class="px-6 py-2 rounded-full bg-white text-gray-900 font-semibold shadow-sm transition-all duration-300">
          Monthly
        </button>
        <button class="px-6 py-2 rounded-full text-gray-600 font-semibold transition-all duration-300 hover:text-gray-900">
          Annual <span class="text-green-600 text-sm ml-1">Save 20%</span>
        </button>
      </div>
    </div>
    
    <!-- 3D Pricing Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12 perspective-1000">
      <!-- Starter Plan -->
      <div class="pricing-card group" data-tilt data-tilt-max="5" data-tilt-speed="1000">
        <div class="relative bg-white rounded-3xl shadow-xl overflow-hidden transform transition-all duration-500 hover:scale-105 hover:shadow-2xl">
          <div class="absolute top-0 right-0 -mr-16 -mt-16 w-32 h-32 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full opacity-50"></div>
          
          <div class="relative p-8">
            <div class="mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
              <p class="text-gray-600">Perfect for small businesses</p>
            </div>
            
            <div class="mb-8">
              <div class="flex items-baseline">
                <span class="text-5xl font-bold text-gray-900">$99</span>
                <span class="text-gray-600 ml-2">/month</span>
              </div>
              <p class="text-sm text-gray-500 mt-2">Billed monthly</p>
            </div>
            
            <ul class="space-y-4 mb-8">
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Up to <strong>1,000</strong> conversations/month</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"><strong>2</strong> AI agents</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Basic analytics dashboard</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Email support</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">1 e-commerce integration</span>
              </li>
            </ul>
            
            <button class="w-full py-4 px-6 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105">
              Start Free Trial
            </button>
          </div>
        </div>
      </div>
      
      <!-- Professional Plan (Highlighted) -->
      <div class="pricing-card group transform md:-translate-y-4" data-tilt data-tilt-max="5" data-tilt-speed="1000">
        <div class="relative bg-gradient-to-br from-purple-600 to-pink-600 rounded-3xl shadow-2xl overflow-hidden transform transition-all duration-500 hover:scale-105 hover:shadow-3xl">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-yellow-400 text-gray-900 px-4 py-1 rounded-full text-sm font-bold">MOST POPULAR</span>
          </div>
          
          <div class="absolute top-0 right-0 -mr-16 -mt-16 w-32 h-32 bg-white rounded-full opacity-10"></div>
          
          <div class="relative p-8 text-white">
            <div class="mb-8">
              <h3 class="text-2xl font-bold mb-2">Professional</h3>
              <p class="text-purple-100">For growing companies</p>
            </div>
            
            <div class="mb-8">
              <div class="flex items-baseline">
                <span class="text-5xl font-bold">$299</span>
                <span class="text-purple-100 ml-2">/month</span>
              </div>
              <p class="text-sm text-purple-200 mt-2">Billed monthly</p>
            </div>
            
            <ul class="space-y-4 mb-8">
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Up to <strong>10,000</strong> conversations/month</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span><strong>5</strong> AI agents</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Advanced analytics & insights</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Priority support</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>All integrations</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Custom AI training</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>API access</span>
              </li>
            </ul>
            
            <button class="w-full py-4 px-6 bg-white hover:bg-gray-100 text-gray-900 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
              Start Free Trial
            </button>
          </div>
        </div>
      </div>
      
      <!-- Enterprise Plan -->
      <div class="pricing-card group" data-tilt data-tilt-max="5" data-tilt-speed="1000">
        <div class="relative bg-white rounded-3xl shadow-xl overflow-hidden transform transition-all duration-500 hover:scale-105 hover:shadow-2xl">
          <div class="absolute top-0 right-0 -mr-16 -mt-16 w-32 h-32 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full opacity-50"></div>
          
          <div class="relative p-8">
            <div class="mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
              <p class="text-gray-600">For large organizations</p>
            </div>
            
            <div class="mb-8">
              <div class="flex items-baseline">
                <span class="text-5xl font-bold text-gray-900">Custom</span>
              </div>
              <p class="text-sm text-gray-500 mt-2">Contact sales for pricing</p>
            </div>
            
            <ul class="space-y-4 mb-8">
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"><strong>Unlimited</strong> conversations</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700"><strong>Unlimited</strong> AI agents</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Enterprise analytics</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">24/7 phone support</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Custom integrations</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">SLA guarantee</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700">Dedicated success manager</span>
              </li>
            </ul>
            
            <button class="w-full py-4 px-6 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105">
              Contact Sales
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Feature Comparison -->
    <div class="mt-20 text-center">
      <button class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors">
        <span>Compare all features</span>
        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
    </div>
  </div>
</section>

<style>
  /* 3D Tilt Effect */
  .pricing-card {
    transform-style: preserve-3d;
    transform: perspective(1000px);
  }
  
  .pricing-card:hover {
    transform: perspective(1000px) rotateY(5deg);
  }
  
  /* Enhanced shadow for 3D effect */
  .shadow-3xl {
    box-shadow: 0 35px 60px -15px rgba(0, 0, 0, 0.3);
  }
</style>

<!-- Include Vanilla Tilt for 3D effect -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.7.0/vanilla-tilt.min.js"></script>