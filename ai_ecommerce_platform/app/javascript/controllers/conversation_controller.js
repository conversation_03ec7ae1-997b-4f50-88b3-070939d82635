import { Controller } from "@hotwired/stimulus"
import consumer from "channels/consumer"

// Connects to data-controller="conversation"
export default class extends Controller {
  static targets = ["messages", "input", "form"]
  
  connect() {
    this.scrollToBottom()
    this.setupChannel()
  }
  
  disconnect() {
    if (this.channel) {
      this.channel.unsubscribe()
    }
  }
  
  setupChannel() {
    const conversationId = this.data.get("conversationId")
    
    this.channel = consumer.subscriptions.create(
      {
        channel: "ConversationChannel",
        id: conversationId
      },
      {
        connected: () => {
          console.log("Connected to conversation channel")
        },
        
        disconnected: () => {
          console.log("Disconnected from conversation channel")
        },
        
        received: (data) => {
          if (data.type === "message") {
            this.appendMessage(data.message)
          }
        }
      }
    )
  }
  
  appendMessage(messageHtml) {
    this.messagesTarget.insertAdjacentHTML("beforeend", messageHtml)
    this.scrollToBottom()
  }
  
  scrollToBottom() {
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
  }
  
  clearInput() {
    this.inputTarget.value = ""
    this.inputTarget.focus()
  }
  
  handleSubmit(event) {
    event.preventDefault()
    
    if (this.inputTarget.value.trim() === "") {
      return
    }
    
    // Submit form via Turbo
    this.formTarget.requestSubmit()
    
    // Clear input immediately for better UX
    const message = this.inputTarget.value
    this.clearInput()
    
    // Show typing indicator
    this.showTypingIndicator()
  }
  
  showTypingIndicator() {
    const typingHtml = `
      <div class="typing-indicator flex justify-start mb-4" data-conversation-target="typing">
        <div class="flex items-end space-x-2">
          <div class="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-white text-sm font-semibold">
            AI
          </div>
          <div class="bg-gray-100 rounded-lg px-4 py-2">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
            </div>
          </div>
        </div>
      </div>
    `
    
    this.messagesTarget.insertAdjacentHTML("beforeend", typingHtml)
    this.scrollToBottom()
  }
  
  removeTypingIndicator() {
    const typingIndicator = this.messagesTarget.querySelector(".typing-indicator")
    if (typingIndicator) {
      typingIndicator.remove()
    }
  }
  
  messageAdded() {
    this.removeTypingIndicator()
    this.scrollToBottom()
  }
}
