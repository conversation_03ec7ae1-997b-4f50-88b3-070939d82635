import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "strengthBar", "strengthText"]
  
  connect() {
    this.checkStrength()
  }
  
  checkStrength() {
    const password = this.inputTarget.value
    let strength = 0
    
    // Length check
    if (password.length >= 8) strength++
    if (password.length >= 12) strength++
    
    // Character variety checks
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++
    if (/[0-9]/.test(password)) strength++
    if (/[^a-zA-Z0-9]/.test(password)) strength++
    
    // Cap at 4
    strength = Math.min(strength, 4)
    
    // Update UI
    this.updateStrengthIndicator(strength)
  }
  
  updateStrengthIndicator(strength) {
    const strengthTexts = ['Too short', 'Weak', 'Fair', 'Good', 'Strong']
    const strengthClasses = [
      'bg-red-500',
      'bg-red-500', 
      'bg-yellow-500',
      'bg-yellow-400',
      'bg-green-500'
    ]
    const widths = ['0%', '25%', '50%', '75%', '100%']
    
    // Update bar
    this.strengthBarTarget.className = `h-2 rounded-full transition-all duration-300 ${strengthClasses[strength]}`
    this.strengthBarTarget.style.width = widths[strength]
    
    // Update text
    this.strengthTextTarget.textContent = strengthTexts[strength]
  }
}