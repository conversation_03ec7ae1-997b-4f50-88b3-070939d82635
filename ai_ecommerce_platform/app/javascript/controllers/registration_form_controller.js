import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["step", "indicator", "form"]
  
  connect() {
    this.currentStep = 1
    this.showStep(1)
  }
  
  nextStep(event) {
    event.preventDefault()
    
    if (this.validateCurrentStep()) {
      this.showStep(this.currentStep + 1)
    }
  }
  
  prevStep(event) {
    event.preventDefault()
    this.showStep(this.currentStep - 1)
  }
  
  showStep(stepNumber) {
    // Update step visibility
    this.stepTargets.forEach((step, index) => {
      step.style.display = index + 1 === stepNumber ? 'block' : 'none'
      step.classList.toggle('active', index + 1 === stepNumber)
    })
    
    // Update indicators
    this.indicatorTargets.forEach((indicator, index) => {
      const indicatorStep = parseInt(indicator.dataset.step)
      
      if (indicatorStep < stepNumber) {
        indicator.classList.add('completed')
        indicator.classList.remove('active')
      } else if (indicatorStep === stepNumber) {
        indicator.classList.add('active')
        indicator.classList.remove('completed')
      } else {
        indicator.classList.remove('active', 'completed')
      }
    })
    
    this.currentStep = stepNumber
  }
  
  validateCurrentStep() {
    const currentStepElement = this.stepTargets[this.currentStep - 1]
    const inputs = currentStepElement.querySelectorAll('input[required], select[required]')
    let isValid = true
    
    inputs.forEach(input => {
      if (!input.value.trim()) {
        isValid = false
        input.classList.add('border-red-500')
        
        // Add error message if not exists
        if (!input.nextElementSibling?.classList.contains('error-message')) {
          const error = document.createElement('p')
          error.className = 'error-message mt-1 text-sm text-red-600'
          error.textContent = 'This field is required'
          input.parentElement.appendChild(error)
        }
      } else {
        input.classList.remove('border-red-500')
        // Remove error message if exists
        const error = input.parentElement.querySelector('.error-message')
        if (error) error.remove()
      }
    })
    
    // Special validation for step 1 (password match)
    if (this.currentStep === 1) {
      const password = this.formTarget.querySelector('input[name="user[password]"]').value
      const passwordConfirmation = this.formTarget.querySelector('input[name="user[password_confirmation]"]').value
      const confirmInput = this.formTarget.querySelector('input[name="user[password_confirmation]"]')
      
      if (password && passwordConfirmation && password !== passwordConfirmation) {
        isValid = false
        confirmInput.classList.add('border-red-500')
        
        if (!confirmInput.nextElementSibling?.classList.contains('error-message')) {
          const error = document.createElement('p')
          error.className = 'error-message mt-1 text-sm text-red-600'
          error.textContent = 'Passwords do not match'
          confirmInput.parentElement.appendChild(error)
        }
      } else if (password === passwordConfirmation) {
        confirmInput.classList.remove('border-red-500')
        const error = confirmInput.parentElement.querySelector('.error-message')
        if (error && error.textContent === 'Passwords do not match') error.remove()
      }
    }
    
    // Special validation for step 3 (terms checkbox)
    if (this.currentStep === 3) {
      const termsCheckbox = currentStepElement.querySelector('input[name="terms"]')
      if (!termsCheckbox.checked) {
        isValid = false
        termsCheckbox.classList.add('border-red-500')
        
        if (!termsCheckbox.parentElement.nextElementSibling?.classList.contains('error-message')) {
          const error = document.createElement('p')
          error.className = 'error-message mt-1 text-sm text-red-600'
          error.textContent = 'You must agree to the terms'
          termsCheckbox.parentElement.parentElement.appendChild(error)
        }
      }
    }
    
    return isValid
  }
  
  handleSubmit(event) {
    if (!this.validateCurrentStep()) {
      event.preventDefault()
    }
  }
}