import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return
    
    // Check contrast ratios on theme change
    this.checkAllContrasts()
    
    // Listen for theme changes
    const observer = new MutationObserver(() => {
      this.checkAllContrasts()
    })
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })
  }
  
  checkAllContrasts() {
    const isDark = document.documentElement.classList.contains('dark')
    console.log(`=== Contrast Check (${isDark ? 'Dark' : 'Light'} Mode) ===`)
    
    // Get computed styles
    const styles = getComputedStyle(document.documentElement)
    
    // Define color pairs to check
    const colorPairs = [
      {
        name: 'Primary Text on Background',
        fg: styles.getPropertyValue('--color-text-primary').trim(),
        bg: styles.getPropertyValue('--color-background').trim()
      },
      {
        name: 'Secondary Text on Background',
        fg: styles.getPropertyValue('--color-text-secondary').trim(),
        bg: styles.getPropertyValue('--color-background').trim()
      },
      {
        name: 'Primary Text on Surface',
        fg: styles.getPropertyValue('--color-text-primary').trim(),
        bg: styles.getPropertyValue('--color-surface').trim()
      },
      {
        name: 'Button Text on Primary',
        fg: styles.getPropertyValue('--color-btn-primary-text').trim(),
        bg: styles.getPropertyValue('--color-primary').trim()
      },
      {
        name: 'Error Text on Background',
        fg: styles.getPropertyValue('--color-error').trim(),
        bg: styles.getPropertyValue('--color-background').trim()
      }
    ]
    
    colorPairs.forEach(pair => {
      const ratio = this.getContrastRatio(pair.fg, pair.bg)
      const wcagAA = ratio >= 4.5
      const wcagAAA = ratio >= 7
      
      console.log(`${pair.name}:`)
      console.log(`  Foreground: ${pair.fg}`)
      console.log(`  Background: ${pair.bg}`)
      console.log(`  Ratio: ${ratio.toFixed(2)}:1`)
      console.log(`  WCAG AA: ${wcagAA ? '✓ PASS' : '✗ FAIL'}`)
      console.log(`  WCAG AAA: ${wcagAAA ? '✓ PASS' : '✗ FAIL'}`)
      console.log('')
    })
  }
  
  // Convert hex to RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }
  
  // Calculate relative luminance
  getLuminance(rgb) {
    const rsRGB = rgb.r / 255
    const gsRGB = rgb.g / 255
    const bsRGB = rgb.b / 255
    
    const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4)
    const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4)
    const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4)
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b
  }
  
  // Calculate contrast ratio
  getContrastRatio(color1, color2) {
    const rgb1 = this.hexToRgb(color1)
    const rgb2 = this.hexToRgb(color2)
    
    if (!rgb1 || !rgb2) return 0
    
    const lum1 = this.getLuminance(rgb1)
    const lum2 = this.getLuminance(rgb2)
    
    const lighter = Math.max(lum1, lum2)
    const darker = Math.min(lum1, lum2)
    
    return (lighter + 0.05) / (darker + 0.05)
  }
}