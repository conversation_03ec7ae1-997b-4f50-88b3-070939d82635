import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button"]
  
  connect() {
    // Initialize theme from cookie or default to light
    this.applyTheme()
  }
  
  toggle() {
    const html = document.documentElement
    const isDark = html.classList.contains('dark')
    
    if (isDark) {
      html.classList.remove('dark')
      document.cookie = "theme=light; path=/; max-age=31536000"
    } else {
      html.classList.add('dark')
      document.cookie = "theme=dark; path=/; max-age=31536000"
    }
  }
  
  applyTheme() {
    const theme = this.getCookie('theme')
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }
  
  getCookie(name) {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  }
}