@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root variables for theme colors */
:root {
  /* Light theme colors - Optimized for contrast */
  --color-background: #fafbfc;
  --color-surface: #ffffff;
  --color-surface-hover: #f8f9fa;
  --color-text-primary: #0f172a; /* Deep blue-gray for maximum contrast */
  --color-text-secondary: #475569; /* Darker than before for AA compliance */
  --color-text-tertiary: #64748b;
  --color-border: #e5e7eb;
  --color-border-hover: #d1d5db;
  
  /* Teal accent colors - Adjusted for better contrast */
  --color-primary-50: #f0fdfa;
  --color-primary-100: #ccfbf1;
  --color-primary-200: #99f6e4;
  --color-primary-300: #5eead4;
  --color-primary-400: #2dd4bf;
  --color-primary-500: #0d9488; /* Darker teal for better contrast on white */
  --color-primary-600: #0f766e;
  --color-primary-700: #115e59;
  --color-primary-800: #134e4a;
  --color-primary-900: #083344;
  
  /* Light mode specific colors */
  --color-primary: #0d9488;
  --color-primary-hover: #0f766e;
  --color-primary-active: #115e59;
  --color-secondary: rgba(243, 244, 246, 1);
  --color-secondary-hover: rgba(229, 231, 235, 1);
  --color-secondary-active: rgba(209, 213, 219, 1);
  --color-error: #dc2626;
  --color-error-bg: rgba(220, 38, 38, 0.1);
  --color-success: #059669;
  --color-success-bg: rgba(5, 150, 105, 0.1);
  --color-warning: #d97706;
  --color-warning-bg: rgba(217, 119, 6, 0.1);
  --color-info: #2563eb;
  --color-info-bg: rgba(37, 99, 235, 0.1);
  --color-focus-ring: rgba(13, 148, 136, 0.4);
  --color-btn-primary-text: #ffffff;
  
  /* Spacing variables */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;
  
  /* Typography */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;
  
  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
  
  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
}

/* Dark theme - Optimized for contrast and accessibility */
.dark {
  /* Core backgrounds */
  --color-background: #0a0b0b; /* Deeper black for better contrast */
  --color-surface: #141616; /* Slightly lighter surface */
  --color-surface-hover: #1a1d1d;
  
  /* Text colors with improved contrast ratios */
  --color-text-primary: #f5f5f5; /* WCAG AA compliant */
  --color-text-secondary: #a8a8a8; /* Increased from 0.7 opacity for better readability */
  --color-text-tertiary: #757575; /* For less important text */
  
  /* Teal accent colors - adjusted for dark backgrounds */
  --color-primary: #14b8a6; /* Main teal */
  --color-primary-hover: #10a394;
  --color-primary-active: #0d8f82;
  --color-primary-50: rgba(20, 184, 166, 0.1);
  --color-primary-100: rgba(20, 184, 166, 0.2);
  
  /* Secondary colors */
  --color-secondary: rgba(156, 163, 175, 0.1);
  --color-secondary-hover: rgba(156, 163, 175, 0.15);
  --color-secondary-active: rgba(156, 163, 175, 0.2);
  
  /* Borders with better visibility */
  --color-border: #2a2d2d;
  --color-border-hover: #3a3d3d;
  
  /* Specific dark mode surfaces */
  --color-sidebar-bg: #0f1111;
  --color-chat-bg: #0a0b0b;
  --color-panel-bg: #141616;
  --color-input-bg: #1a1d1d;
  
  /* Status colors optimized for dark mode */
  --color-error: #ef4444; /* Brighter red for visibility */
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-success: #10b981; /* Green with good contrast */
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-warning: #f59e0b; /* Amber for warnings */
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-info: #3b82f6; /* Blue for info */
  --color-info-bg: rgba(59, 130, 246, 0.1);
  
  /* Focus and interaction states */
  --color-focus-ring: rgba(20, 184, 166, 0.5);
  --color-btn-primary-text: #0a0b0b; /* Dark text on teal buttons */
  
  /* Enhanced shadows for depth */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-text-primary font-sans antialiased;
    font-feature-settings: "cv11", "ss01";
  }
  
  /* Smooth transitions for theme switching */
  body,
  .bg-surface,
  .bg-background,
  .text-primary,
  .text-secondary,
  .border-border {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Dashboard Layout */
  .dashboard {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    grid-template-rows: 100vh;
    background: var(--color-background);
    overflow: hidden;
  }

  .dashboard.sidebar-collapsed {
    grid-template-columns: 60px 1fr 320px;
  }

  /* Sidebar Styles */
  .sidebar {
    background: var(--color-sidebar-bg);
    border-right: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    overflow-y: auto;
  }

  .sidebar-header {
    padding: 20px 16px;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .logo-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-primary);
    border-radius: 8px;
    color: var(--color-btn-primary-text);
  }

  .logo h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .sidebar-toggle {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .sidebar-toggle:hover {
    background: var(--color-secondary);
    color: var(--color-text-primary);
  }

  .sidebar-nav {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .nav-section {
    margin-bottom: 24px;
  }

  .nav-section h3 {
    font-size: 12px;
    font-weight: 550;
    color: var(--color-text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .agent-list, .quick-actions {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .agent-item, .nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--color-text-primary);
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
    margin-bottom: 4px;
    gap: 12px;
    font-size: 14px;
  }

  .agent-item:hover, .nav-item:hover {
    background: var(--color-secondary);
  }

  .agent-item.active {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
  }

  .agent-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 14px;
    flex-shrink: 0;
  }

  .agent-info {
    flex: 1;
    min-width: 0;
  }

  .agent-name {
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .agent-description {
    font-size: 12px;
    color: var(--color-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
  }

  .agent-item.active .agent-description {
    color: rgba(252, 252, 249, 0.8);
  }

  .agent-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-success);
    flex-shrink: 0;
  }

  .agent-status.offline {
    background: var(--color-text-secondary);
  }

  .nav-links {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--color-text-primary);
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .nav-link:hover {
    background: var(--color-secondary);
    color: var(--color-text);
  }

  .nav-section-title {
    font-size: 12px;
    font-weight: 550;
    color: var(--color-text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .logo-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  /* Main Content Area */
  .main-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--color-chat-bg);
  }

  /* Header */
  .header {
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .current-agent-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 9999px;
    background: rgba(33, 128, 141, 0.1);
    color: var(--color-primary);
  }

  .status-indicator.online {
    background: rgba(34, 197, 94, 0.1);
    color: rgba(34, 197, 94, 1);
  }

  .dark .status-indicator.online {
    background: rgba(34, 197, 94, 0.15);
    color: rgba(74, 222, 128, 1);
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .theme-toggle {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .theme-toggle:hover {
    background: var(--color-secondary);
    color: var(--color-text-primary);
  }

  .user-avatar {
    background: none;
    border: none;
    cursor: pointer;
  }

  .avatar-circle {
    width: 36px;
    height: 36px;
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
  }
  /* Card component */
  /* Card component with enhanced contrast */
  .card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    @apply rounded-xl p-6 transition-all duration-200;
  }
  
  .card:hover {
    border-color: var(--color-border-hover);
    box-shadow: var(--shadow-md);
  }
  
  .dark .card {
    box-shadow: var(--shadow-sm);
  }
  
  /* Chat Input */
  .chat-input-container {
    border-top: 1px solid var(--color-border);
    background: var(--color-surface);
    padding: var(--space-16) var(--space-24);
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
  }

  .typing-dots {
    display: flex;
    gap: var(--space-2);
  }

  .typing-dots span {
    width: 4px;
    height: 4px;
    background: var(--color-text-secondary);
    border-radius: 50%;
    animation: typing 1.4s infinite;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0%, 60%, 100% { opacity: 0.2; }
    30% { opacity: 1; }
  }

  .chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    transition: border-color var(--duration-fast) var(--ease-standard);
  }

  .chat-input-wrapper:focus-within {
    border-color: var(--color-primary);
  }

  .input-actions {
    display: flex;
    gap: var(--space-4);
  }

  .input-btn {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: var(--space-8);
    border-radius: var(--radius-base);
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .input-btn:hover {
    background: var(--color-secondary);
    color: var(--color-text);
  }

  .chat-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--color-text);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    font-family: var(--font-family-base);
  }

  .chat-input::placeholder {
    color: var(--color-text-secondary);
  }

  .send-btn {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border: none;
    padding: var(--space-8);
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .send-btn:hover {
    background: var(--color-primary-hover);
  }

  .send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .quick-responses {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-8);
    margin-top: var(--space-12);
  }

  .quick-response-btn {
    background: var(--color-secondary);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    padding: var(--space-6) var(--space-12);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .quick-response-btn:hover {
    background: var(--color-secondary-hover);
    border-color: var(--color-primary);
  }
  
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-surface border border-border text-text-primary hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .dark .btn-secondary {
    @apply hover:bg-gray-800 border-gray-700;
  }
  
  /* Input fields with improved contrast */
  .input-field {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    color: var(--color-text-primary);
    @apply w-full px-3 py-2 rounded-lg text-sm;
    @apply placeholder:text-text-secondary focus:outline-none focus:ring-2 focus:border-transparent;
    @apply transition-all duration-200;
  }
  
  .input-field:focus {
    ring-color: var(--color-focus-ring);
  }
  
  .dark .input-field {
    background-color: var(--color-input-bg);
    @apply focus:ring-offset-1 focus:ring-offset-gray-900;
  }
  
  /* Ensure placeholder text has sufficient contrast */
  .input-field::placeholder {
    color: var(--color-text-secondary);
    opacity: 0.8;
  }
  
  /* Chat Interface */
  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-24);
    display: flex;
    flex-direction: column;
    gap: var(--space-16);
  }

  .message {
    display: flex;
    flex-direction: column;
    max-width: 70%;
    animation: fadeIn var(--duration-normal) var(--ease-standard);
  }

  .message.user {
    align-self: flex-end;
    align-items: flex-end;
  }

  .message.bot {
    align-self: flex-start;
  }

  .message-header {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
  }

  .message-sender {
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
  }

  .message-time {
    color: var(--color-text-secondary);
    font-size: var(--font-size-xs);
  }

  .message-content {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-12) var(--space-16);
    box-shadow: var(--shadow-sm);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  .message.user .message-content {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border-color: var(--color-primary);
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-lg);
  }

  .message.bot .message-content {
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-lg) var(--radius-sm);
  }

  .message.user .message-time {
    text-align: right;
    margin-top: var(--space-4);
  }
  
  /* Right Sidebar */
  .right-sidebar {
    background: var(--color-surface);
    border-left: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .sidebar-tabs {
    display: flex;
    background: var(--color-background);
    border-bottom: 1px solid var(--color-border);
  }

  .tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: var(--space-12) var(--space-16);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
    border-bottom: 2px solid transparent;
  }

  .tab-btn.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
  }

  .tab-btn:hover {
    color: var(--color-text);
  }

  .tab-content {
    flex: 1;
    padding: var(--space-16);
    overflow-y: auto;
    display: none;
  }

  .tab-content.active {
    display: block;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    margin-bottom: var(--space-24);
  }

  .metric-card {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-12);
    text-align: center;
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .metric-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }

  .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-4);
  }

  .metric-label {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Charts and graphs */
  .chart-container {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-16);
    margin-bottom: var(--space-24);
  }

  .chart-container h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-12);
    color: var(--color-text);
  }

  /* Insights Panel */
  .insights-panel h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-12);
    color: var(--color-text);
  }

  .insight-item {
    display: flex;
    gap: var(--space-12);
    padding: var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-12);
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .insight-item:hover {
    border-color: var(--color-primary);
  }

  .insight-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .insight-content {
    flex: 1;
  }

  .insight-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    line-height: 1.4;
  }

  .insight-confidence {
    display: inline-block;
    margin-top: var(--space-4);
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: var(--color-secondary);
    padding: var(--space-2) var(--space-6);
    border-radius: var(--radius-sm);
  }
  
  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    background-color: var(--color-success-bg);
    color: var(--color-success);
    @apply font-medium;
  }
  
  .dark .badge-success {
    background-color: rgba(16, 185, 129, 0.15);
    color: #34d399; /* Lighter green for dark mode */
  }
  
  .badge-warning {
    background-color: var(--color-warning-bg);
    color: var(--color-warning);
    @apply font-medium;
  }
  
  .dark .badge-warning {
    background-color: rgba(245, 158, 11, 0.15);
    color: #fbbf24; /* Lighter amber for dark mode */
  }
  
  .badge-danger {
    background-color: var(--color-error-bg);
    color: var(--color-error);
    @apply font-medium;
  }
  
  .dark .badge-danger {
    background-color: rgba(239, 68, 68, 0.15);
    color: #f87171; /* Lighter red for dark mode */
  }
  
  .badge-info {
    background-color: var(--color-info-bg);
    color: var(--color-info);
    @apply font-medium;
  }
  
  .dark .badge-info {
    background-color: rgba(20, 184, 166, 0.15);
    color: var(--color-primary); /* Use primary teal */
  }
  
  /* Scrollbar styling */
  .dark ::-webkit-scrollbar {
    @apply w-2;
  }
  
  .dark ::-webkit-scrollbar-track {
    @apply bg-gray-900;
  }
  
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-gray-700 rounded-full;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-600;
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .dark .skeleton {
    @apply bg-gray-700;
  }
  
  /* Tooltips */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg;
  }
  
  .dark .tooltip {
    @apply bg-gray-700;
  }
}

@layer utilities {
  /* Custom text colors using CSS variables */
  .text-primary {
    color: var(--color-text-primary);
  }
  
  .text-secondary {
    color: var(--color-text-secondary);
  }
  
  .text-tertiary {
    color: var(--color-text-tertiary);
  }
  
  /* Custom background colors */
  .bg-background {
    background-color: var(--color-background);
  }
  
  .bg-surface {
    background-color: var(--color-surface);
  }
  
  .bg-surface-hover {
    background-color: var(--color-surface-hover);
  }
  
  /* Custom border colors */
  .border-border {
    border-color: var(--color-border);
  }
  
  .border-border-hover {
    border-color: var(--color-border-hover);
  }
  
  /* Shadow utilities for dark mode */
  .shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-large {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  }
  
  .dark .shadow-soft,
  .dark .shadow-medium,
  .dark .shadow-large {
    box-shadow: none;
  }
  
  /* Animation utilities */
  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Workflow and Settings Styles */
  .workflow-item {
    padding: var(--space-16);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-12);
  }
  
  .workflow-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
  }
  
  .workflow-header h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    margin: 0;
  }
  
  .workflow-item p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0;
  }
  
  .settings-section {
    margin-bottom: var(--space-24);
  }
  
  .settings-section h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-16);
  }
  
  .setting-item {
    margin-bottom: var(--space-16);
  }
  
  .setting-item label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
    margin-bottom: var(--space-6);
  }
  
  .setting-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-border);
    transition: all var(--duration-fast) var(--ease-standard);
    border-radius: var(--radius-full);
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all var(--duration-fast) var(--ease-standard);
    border-radius: 50%;
  }
  
  .toggle-switch input:checked + .slider {
    background-color: var(--color-primary);
  }
  
  .toggle-switch input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  .toggle-switch input:focus + .slider {
    box-shadow: var(--focus-ring);
  }
  
  /* Glassmorphism effect for dark mode */
  .dark .glass {
    @apply bg-gray-900/80 backdrop-blur-sm border-gray-800;
  }

  /* Mobile Responsive Styles */
  @media (max-width: 768px) {
    .dashboard {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: -280px;
      width: 280px;
      height: 100vh;
      z-index: 1001;
      transition: left 250ms cubic-bezier(0.16, 1, 0.3, 1);
    }

    .sidebar.active {
      left: 0;
    }

    .right-sidebar {
      position: fixed;
      top: 0;
      right: -320px;
      width: 320px;
      height: 100vh;
      z-index: 1001;
      transition: right 250ms cubic-bezier(0.16, 1, 0.3, 1);
    }

    .right-sidebar.active {
      right: 0;
    }

    .main-content {
      grid-column: 1;
      grid-row: 1 / -1;
    }
  }

  /* Mobile Overlay */
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .mobile-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}