@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root variables for theme colors */
:root {
  /* Light theme colors */
  --color-background: #f8fafc;
  --color-surface: #ffffff;
  --color-surface-hover: #f9fafb;
  --color-text-primary: #0f172a;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-border: #e2e8f0;
  --color-border-hover: #cbd5e1;
  
  /* Teal accent colors */
  --color-primary-50: #f0fdfa;
  --color-primary-100: #ccfbf1;
  --color-primary-200: #99f6e4;
  --color-primary-300: #5eead4;
  --color-primary-400: #2dd4bf;
  --color-primary-500: #14b8a6;
  --color-primary-600: #0d9488;
  --color-primary-700: #0f766e;
  --color-primary-800: #115e59;
  --color-primary-900: #134e4a;
  
  /* Spacing variables */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;
  
  /* Typography */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;
  
  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
  
  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
}

/* Dark theme - Matching the mockups exactly */
.dark {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text-primary: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-border-hover: rgba(119, 124, 124, 0.4);
  
  /* Specific dark mode surfaces */
  --color-sidebar-bg: rgba(31, 33, 33, 1);
  --color-chat-bg: rgba(31, 33, 33, 1);
  --color-panel-bg: rgba(38, 40, 40, 1);
  --color-input-bg: rgba(38, 40, 40, 1);
  
  /* Dark mode specific */
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-text-primary font-sans antialiased;
    font-feature-settings: "cv11", "ss01";
  }
  
  /* Smooth transitions for theme switching */
  body,
  .bg-surface,
  .bg-background,
  .text-primary,
  .text-secondary,
  .border-border {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Dashboard Layout */
  .dashboard {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    grid-template-rows: 100vh;
    background: var(--color-background);
    overflow: hidden;
  }

  .dashboard.sidebar-collapsed {
    grid-template-columns: 60px 1fr 320px;
  }

  /* Sidebar Styles */
  .sidebar {
    background: var(--color-sidebar-bg);
    border-right: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    overflow-y: auto;
  }

  .sidebar-header {
    padding: 20px 16px;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .logo-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-primary);
    border-radius: 8px;
    color: var(--color-btn-primary-text);
  }

  .logo h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .sidebar-toggle {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .sidebar-toggle:hover {
    background: var(--color-secondary);
    color: var(--color-text-primary);
  }

  .sidebar-nav {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .nav-section {
    margin-bottom: 24px;
  }

  .nav-section h3 {
    font-size: 12px;
    font-weight: 550;
    color: var(--color-text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .agent-list, .quick-actions {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .agent-item, .nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--color-text-primary);
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
    margin-bottom: 4px;
    gap: 12px;
    font-size: 14px;
  }

  .agent-item:hover, .nav-item:hover {
    background: var(--color-secondary);
  }

  .agent-item.active {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
  }

  .agent-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 14px;
    flex-shrink: 0;
  }

  .agent-info {
    flex: 1;
    min-width: 0;
  }

  .agent-name {
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .agent-description {
    font-size: 12px;
    color: var(--color-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
  }

  .agent-item.active .agent-description {
    color: rgba(252, 252, 249, 0.8);
  }

  /* Main Content Area */
  .main-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--color-chat-bg);
  }

  /* Header */
  .header {
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .current-agent-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 9999px;
    background: rgba(33, 128, 141, 0.1);
    color: var(--color-primary);
  }

  .status-indicator.online {
    background: rgba(34, 197, 94, 0.1);
    color: rgba(34, 197, 94, 1);
  }

  .dark .status-indicator.online {
    background: rgba(34, 197, 94, 0.15);
    color: rgba(74, 222, 128, 1);
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .theme-toggle {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .theme-toggle:hover {
    background: var(--color-secondary);
    color: var(--color-text-primary);
  }

  .user-avatar {
    background: none;
    border: none;
    cursor: pointer;
  }

  .avatar-circle {
    width: 36px;
    height: 36px;
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
  }
  /* Card component */
  .card {
    @apply bg-surface rounded-xl border border-border shadow-soft p-6 transition-all duration-200;
  }
  
  .card:hover {
    @apply shadow-medium border-border-hover;
  }
  
  /* Chat Input */
  .chat-input-container {
    border-top: 1px solid var(--color-border);
    background: var(--color-surface);
    padding: var(--space-16) var(--space-24);
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
  }

  .typing-dots {
    display: flex;
    gap: var(--space-2);
  }

  .typing-dots span {
    width: 4px;
    height: 4px;
    background: var(--color-text-secondary);
    border-radius: 50%;
    animation: typing 1.4s infinite;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0%, 60%, 100% { opacity: 0.2; }
    30% { opacity: 1; }
  }

  .chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-8);
    transition: border-color var(--duration-fast) var(--ease-standard);
  }

  .chat-input-wrapper:focus-within {
    border-color: var(--color-primary);
  }

  .input-actions {
    display: flex;
    gap: var(--space-4);
  }

  .input-btn {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    padding: var(--space-8);
    border-radius: var(--radius-base);
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .input-btn:hover {
    background: var(--color-secondary);
    color: var(--color-text);
  }

  .chat-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--color-text);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    font-family: var(--font-family-base);
  }

  .chat-input::placeholder {
    color: var(--color-text-secondary);
  }

  .send-btn {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border: none;
    padding: var(--space-8);
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .send-btn:hover {
    background: var(--color-primary-hover);
  }

  .send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .quick-responses {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-8);
    margin-top: var(--space-12);
  }

  .quick-response-btn {
    background: var(--color-secondary);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    padding: var(--space-6) var(--space-12);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .quick-response-btn:hover {
    background: var(--color-secondary-hover);
    border-color: var(--color-primary);
  }
  
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-surface border border-border text-text-primary hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .dark .btn-secondary {
    @apply hover:bg-gray-800 border-gray-700;
  }
  
  /* Input fields */
  .input-field {
    @apply w-full px-3 py-2 bg-surface border border-border rounded-lg text-sm;
    @apply placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-all duration-200;
  }
  
  .dark .input-field {
    background-color: var(--color-input-bg);
    @apply border-border focus:border-transparent;
  }
  
  /* Chat Interface */
  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-24);
    display: flex;
    flex-direction: column;
    gap: var(--space-16);
  }

  .message {
    display: flex;
    flex-direction: column;
    max-width: 70%;
    animation: fadeIn var(--duration-normal) var(--ease-standard);
  }

  .message.user {
    align-self: flex-end;
    align-items: flex-end;
  }

  .message.bot {
    align-self: flex-start;
  }

  .message-header {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
  }

  .message-sender {
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
  }

  .message-time {
    color: var(--color-text-secondary);
    font-size: var(--font-size-xs);
  }

  .message-content {
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-12) var(--space-16);
    box-shadow: var(--shadow-sm);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
  }

  .message.user .message-content {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border-color: var(--color-primary);
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-sm) var(--radius-lg);
  }

  .message.bot .message-content {
    border-radius: var(--radius-lg) var(--radius-lg) var(--radius-lg) var(--radius-sm);
  }

  .message.user .message-time {
    text-align: right;
    margin-top: var(--space-4);
  }
  
  /* Right Sidebar */
  .right-sidebar {
    background: var(--color-surface);
    border-left: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .sidebar-tabs {
    display: flex;
    background: var(--color-background);
    border-bottom: 1px solid var(--color-border);
  }

  .tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: var(--space-12) var(--space-16);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
    border-bottom: 2px solid transparent;
  }

  .tab-btn.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
  }

  .tab-btn:hover {
    color: var(--color-text);
  }

  .tab-content {
    flex: 1;
    padding: var(--space-16);
    overflow-y: auto;
    display: none;
  }

  .tab-content.active {
    display: block;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    margin-bottom: var(--space-24);
  }

  .metric-card {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-12);
    text-align: center;
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .metric-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }

  .metric-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-4);
  }

  .metric-label {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Charts and graphs */
  .chart-container {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    padding: var(--space-16);
    margin-bottom: var(--space-24);
  }

  .chart-container h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-12);
    color: var(--color-text);
  }

  /* Insights Panel */
  .insights-panel h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-12);
    color: var(--color-text);
  }

  .insight-item {
    display: flex;
    gap: var(--space-12);
    padding: var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-12);
    transition: all var(--duration-fast) var(--ease-standard);
  }

  .insight-item:hover {
    border-color: var(--color-primary);
  }

  .insight-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .insight-content {
    flex: 1;
  }

  .insight-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    line-height: 1.4;
  }

  .insight-confidence {
    display: inline-block;
    margin-top: var(--space-4);
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    background: var(--color-secondary);
    padding: var(--space-2) var(--space-6);
    border-radius: var(--radius-sm);
  }
  
  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .dark .badge-success {
    @apply bg-green-900/30 text-green-400;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .dark .badge-warning {
    @apply bg-yellow-900/30 text-yellow-400;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  .dark .badge-danger {
    @apply bg-red-900/30 text-red-400;
  }
  
  .badge-info {
    @apply bg-primary-100 text-primary-800;
  }
  
  .dark .badge-info {
    @apply bg-primary-900/30 text-primary-400;
  }
  
  /* Scrollbar styling */
  .dark ::-webkit-scrollbar {
    @apply w-2;
  }
  
  .dark ::-webkit-scrollbar-track {
    @apply bg-gray-900;
  }
  
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-gray-700 rounded-full;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-600;
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .dark .skeleton {
    @apply bg-gray-700;
  }
  
  /* Tooltips */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg;
  }
  
  .dark .tooltip {
    @apply bg-gray-700;
  }
}

@layer utilities {
  /* Custom text colors using CSS variables */
  .text-primary {
    color: var(--color-text-primary);
  }
  
  .text-secondary {
    color: var(--color-text-secondary);
  }
  
  .text-tertiary {
    color: var(--color-text-tertiary);
  }
  
  /* Custom background colors */
  .bg-background {
    background-color: var(--color-background);
  }
  
  .bg-surface {
    background-color: var(--color-surface);
  }
  
  .bg-surface-hover {
    background-color: var(--color-surface-hover);
  }
  
  /* Custom border colors */
  .border-border {
    border-color: var(--color-border);
  }
  
  .border-border-hover {
    border-color: var(--color-border-hover);
  }
  
  /* Shadow utilities for dark mode */
  .shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-large {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  }
  
  .dark .shadow-soft,
  .dark .shadow-medium,
  .dark .shadow-large {
    box-shadow: none;
  }
  
  /* Animation utilities */
  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Workflow and Settings Styles */
  .workflow-item {
    padding: var(--space-16);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-12);
  }
  
  .workflow-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
  }
  
  .workflow-header h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    margin: 0;
  }
  
  .workflow-item p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0;
  }
  
  .settings-section {
    margin-bottom: var(--space-24);
  }
  
  .settings-section h4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-16);
  }
  
  .setting-item {
    margin-bottom: var(--space-16);
  }
  
  .setting-item label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
    margin-bottom: var(--space-6);
  }
  
  .setting-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-border);
    transition: all var(--duration-fast) var(--ease-standard);
    border-radius: var(--radius-full);
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all var(--duration-fast) var(--ease-standard);
    border-radius: 50%;
  }
  
  .toggle-switch input:checked + .slider {
    background-color: var(--color-primary);
  }
  
  .toggle-switch input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  .toggle-switch input:focus + .slider {
    box-shadow: var(--focus-ring);
  }
  
  /* Glassmorphism effect for dark mode */
  .dark .glass {
    @apply bg-gray-900/80 backdrop-blur-sm border-gray-800;
  }
}