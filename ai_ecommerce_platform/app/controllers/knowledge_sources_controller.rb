class KnowledgeSourcesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_knowledge_source, only: [:show, :edit, :update, :destroy, :process_document]

  def index
    @knowledge_sources = current_tenant.knowledge_sources.order(created_at: :desc)
  end

  def show
    @documents = @knowledge_source.documents.order(created_at: :desc)
  end

  def new
    @knowledge_source = current_tenant.knowledge_sources.build
  end

  def create
    @knowledge_source = current_tenant.knowledge_sources.build(knowledge_source_params)
    
    if @knowledge_source.save
      redirect_to @knowledge_source, notice: 'Knowledge source was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @knowledge_source.update(knowledge_source_params)
      redirect_to @knowledge_source, notice: 'Knowledge source was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @knowledge_source.destroy
    redirect_to knowledge_sources_url, notice: 'Knowledge source was successfully destroyed.'
  end

  def process_document
    # This would handle document processing, embedding generation, etc.
    ProcessKnowledgeDocumentJob.perform_later(@knowledge_source)
    redirect_to @knowledge_source, notice: 'Document processing has been queued.'
  end

  private

  def set_knowledge_source
    @knowledge_source = current_tenant.knowledge_sources.find(params[:id])
  end

  def knowledge_source_params
    params.require(:knowledge_source).permit(:name, :description, :source_type, :source_url, :active, :sync_frequency, settings: {})
  end
end