class Users::SessionsController < Devise::SessionsController
  # Skip the default before actions from ApplicationController for Devise
  skip_before_action :authenticate_user!, only: [:new, :create]
  skip_before_action :set_current_tenant, only: [:new, :create]
  
  # Override the after_sign_in_path_for method
  def after_sign_in_path_for(resource)
    stored_location_for(resource) || user_root_path
  end
end