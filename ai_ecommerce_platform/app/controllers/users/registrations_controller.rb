class Users::RegistrationsController < Devise::RegistrationsController
  # Skip the default before actions from ApplicationController for Devise
  skip_before_action :authenticate_user!
  skip_before_action :set_current_tenant
  
  before_action :configure_sign_up_params, only: [:create]
  
  # POST /resource
  def create
    build_resource(sign_up_params)

    resource.save
    yield resource if block_given?
    
    if resource.persisted?
      # Create tenant with company information
      tenant = Tenant.create!(
        name: params[:company_name] || "#{resource.email.split('@').first}'s Company",
        subdomain: generate_subdomain(params[:company_name]),
        settings: {
          company_size: params[:company_size],
          industry: params[:industry],
          interested_features: params[:features] || []
        }
      )
      
      # Create tenant user relationship
      TenantUser.create!(
        tenant: tenant,
        user: resource,
        role: 'owner'
      )
      
      # Set current tenant
      resource.update!(current_tenant: tenant)
      
      if resource.active_for_authentication?
        set_flash_message! :notice, :signed_up
        sign_up(resource_name, resource)
        respond_with resource, location: after_sign_up_path_for(resource)
      else
        set_flash_message! :notice, :"signed_up_but_#{resource.inactive_message}"
        expire_data_after_sign_in!
        respond_with resource, location: after_inactive_sign_up_path_for(resource)
      end
    else
      clean_up_passwords resource
      set_minimum_password_length
      respond_with resource
    end
  end

  protected

  def configure_sign_up_params
    devise_parameter_sanitizer.permit(:sign_up, keys: [:first_name, :last_name])
  end

  def after_sign_up_path_for(resource)
    user_root_path
  end

  private

  def generate_subdomain(company_name)
    return SecureRandom.hex(8) if company_name.blank?
    
    subdomain = company_name.downcase.gsub(/[^a-z0-9]/, '')
    subdomain = subdomain[0..19] if subdomain.length > 20
    
    # Ensure uniqueness
    base_subdomain = subdomain
    counter = 1
    while Tenant.exists?(subdomain: subdomain)
      subdomain = "#{base_subdomain}#{counter}"
      counter += 1
    end
    
    subdomain
  end
end