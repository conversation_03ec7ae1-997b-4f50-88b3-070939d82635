class MessagesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_conversation
  
  def create
    @message = @conversation.messages.build(message_params)
    @message.role = 'user'
    @message.metadata = {
      user_id: current_user.id,
      user_email: current_user.email
    }
    
    if @message.save
      # Process AI response asynchronously
      ProcessMessageJob.perform_later(@message)
      
      respond_to do |format|
        format.turbo_stream
        format.html { redirect_to conversation_path(@conversation) }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("message_form", partial: "messages/form", locals: { conversation: @conversation, message: @message }) }
        format.html { redirect_to conversation_path(@conversation), alert: "Message could not be sent." }
      end
    end
  end
  
  private
  
  def set_conversation
    @conversation = current_user.tenants.first.conversations.find(params[:conversation_id])
  end
  
  def message_params
    params.require(:message).permit(:content)
  end
end
