class DashboardController < ApplicationController

  def index
    unless current_tenant
      redirect_to root_path, alert: "Please select a tenant first" and return
    end
    
    @conversations_today = current_tenant.conversations.where(created_at: Date.current.all_day).count
    @active_conversations = current_tenant.conversations.active.count
    @total_messages = current_tenant.conversations.count # Simplified for now
    @response_time = calculate_average_response_time
    
    @recent_conversations = current_tenant.conversations
      .includes(:messages)
      .order(updated_at: :desc)
      .limit(5)
    
    @top_agents = current_tenant.agents
      .joins(:conversations)
      .group('agents.id')
      .order('COUNT(conversations.id) DESC')
      .limit(3)
  end

  def analytics
    @date_range = params[:date_range] || '7days'
    @metrics = Analytics::DashboardMetrics.new(current_tenant, @date_range).generate
  end

  private

  def calculate_average_response_time
    return 0 unless current_tenant
    
    # For now, return a default value since we may not have messages yet
    0
  end
end