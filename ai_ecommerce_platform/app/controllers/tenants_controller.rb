class TenantsController < ApplicationController
  skip_before_action :set_current_tenant, only: [:new, :create]
  
  def new
    @tenant = Tenant.new
  end

  def create
    @tenant = Tenant.new(tenant_params)
    
    if @tenant.save
      # Create tenant user relationship
      current_user.tenant_users.create!(tenant: @tenant, role: 'owner')
      
      # Create default agents
      create_default_agents(@tenant)
      
      # Set as current tenant
      current_user.update!(current_tenant: @tenant)
      Current.tenant = @tenant
      
      redirect_to root_path, notice: "Welcome to #{@tenant.name}!"
    else
      render :new
    end
  end
  
  private
  
  def tenant_params
    params.require(:tenant).permit(:name, :subdomain)
  end
  
  def create_default_agents(tenant)
    [
      { name: 'Emma', kind: 'customer_service' },
      { name: 'Alex', kind: 'sales' },
      { name: 'Sam', kind: 'technical_support' }
    ].each do |agent_attrs|
      tenant.agents.create!(
        name: agent_attrs[:name],
        kind: agent_attrs[:kind],
        llm_provider: 'openai',
        llm_model: 'gpt-4o-mini'
      )
    end
  end
end
