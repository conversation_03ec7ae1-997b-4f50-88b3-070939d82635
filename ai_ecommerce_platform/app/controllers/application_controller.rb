class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern
  
  # Security - must come first
  protect_from_forgery with: :exception
  
  # Multi-tenancy setup
  set_current_tenant_through_filter
  before_action :authenticate_user!
  before_action :set_current_tenant
  
  private
  
  def set_current_tenant
    return unless user_signed_in? && current_user.persisted?
    
    # Set tenant based on subdomain or user's current tenant
    tenant = find_tenant_from_request || current_user.current_tenant
    
    if tenant
      Current.tenant = tenant
      ActsAsTenant.current_tenant = tenant
    elsif !devise_controller? && !request.path.start_with?('/tenants')
      # Only redirect if we're not already on a devise or tenant page
      redirect_to new_tenant_path, alert: "Please select or create a tenant" and return
    end
  end
  
  def find_tenant_from_request
    # Try to find tenant by subdomain
    if request.subdomain.present? && request.subdomain != 'www'
      Tenant.find_by(subdomain: request.subdomain)
    elsif params[:tenant_id].present?
      current_user.tenants.find_by(id: params[:tenant_id])
    end
  end
  
  def require_tenant_admin!
    unless current_user.tenant_role(Current.tenant).in?(['owner', 'admin'])
      redirect_to root_path, alert: "You don't have permission to access this page"
    end
  end
  
  def set_tenant
    @tenant = Current.tenant
  end
  
  def current_tenant
    Current.tenant
  end
  helper_method :current_tenant
end
