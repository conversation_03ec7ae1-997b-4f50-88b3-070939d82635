class WebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_webhook

  def shopify
    integration = Integration.find_by!(webhook_token: params[:token])
    
    result = Ecommerce::IntegrationFactory
      .create(integration)
      .webhook_handler(webhook_payload)
    
    if result[:success]
      head :ok
    else
      render json: { error: result[:error] }, status: :unprocessable_entity
    end
  end

  def woocommerce
    integration = Integration.find_by!(webhook_token: params[:token])
    
    result = Ecommerce::IntegrationFactory
      .create(integration)
      .webhook_handler(webhook_payload)
    
    if result[:success]
      head :ok
    else
      render json: { error: result[:error] }, status: :unprocessable_entity
    end
  end

  private

  def authenticate_webhook
    case params[:platform]
    when 'shopify'
      authenticate_shopify_webhook
    when 'woocommerce'
      authenticate_woocommerce_webhook
    else
      head :unauthorized
    end
  end

  def authenticate_shopify_webhook
    return true if Rails.env.development?
    
    integration = Integration.find_by!(webhook_token: params[:token])
    
    hmac_header = request.headers['X-Shopify-Hmac-Sha256']
    calculated_hmac = Base64.strict_encode64(
      OpenSSL::HMAC.digest(
        'sha256',
        integration.config['webhook_secret'],
        request.raw_post
      )
    )
    
    unless ActiveSupport::SecurityUtils.secure_compare(calculated_hmac, hmac_header)
      head :unauthorized
    end
  end

  def authenticate_woocommerce_webhook
    return true if Rails.env.development?
    
    integration = Integration.find_by!(webhook_token: params[:token])
    
    signature = request.headers['X-WC-Webhook-Signature']
    calculated_signature = Base64.strict_encode64(
      OpenSSL::HMAC.digest(
        'sha256',
        integration.config['webhook_secret'],
        request.raw_post
      )
    )
    
    unless ActiveSupport::SecurityUtils.secure_compare(calculated_signature, signature)
      head :unauthorized
    end
  end

  def webhook_payload
    {
      platform: params[:platform],
      topic: request.headers['X-Shopify-Topic'] || request.headers['X-WC-Webhook-Topic'],
      data: JSON.parse(request.raw_post)
    }
  end
end