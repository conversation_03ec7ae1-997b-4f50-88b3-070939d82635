module Api
  module V1
    class Chat<PERSON>ontroller < BaseController
      def create
        conversation = find_or_create_conversation
        
        message = conversation.messages.create!(
          content: params[:message],
          sender_type: 'customer',
          metadata: {
            ip_address: request.remote_ip,
            user_agent: request.user_agent,
            page_url: params[:page_url]
          }
        )
        
        ProcessMessageJob.perform_later(message.id)
        
        render json: {
          conversation_id: conversation.id,
          message_id: message.id,
          status: 'processing'
        }
      end

      def show
        conversation = current_tenant.conversations.find_by!(
          session_id: params[:session_id]
        )
        
        messages = conversation.messages.order(:created_at)
        
        render json: {
          conversation: {
            id: conversation.id,
            session_id: conversation.session_id,
            status: conversation.status,
            messages: messages.map { |m| serialize_message(m) }
          }
        }
      end

      def messages
        conversation = current_tenant.conversations.find_by!(
          session_id: params[:session_id]
        )
        
        messages = conversation.messages
        messages = messages.where('created_at > ?', params[:after]) if params[:after]
        messages = messages.order(:created_at)
        
        render json: {
          messages: messages.map { |m| serialize_message(m) }
        }
      end

      private

      def find_or_create_conversation
        session_id = params[:session_id] || SecureRandom.uuid
        
        customer = find_or_create_customer
        
        conversation = current_tenant.conversations.find_or_create_by(
          session_id: session_id
        ) do |conv|
          conv.customer = customer
          conv.status = 'active'
          conv.metadata = {
            source: 'chat_widget',
            page_url: params[:page_url],
            referrer: params[:referrer]
          }
        end
        
        conversation
      end

      def find_or_create_customer
        return nil unless params[:customer_email].present?
        
        current_tenant.customers.find_or_create_by(
          email: params[:customer_email]
        ) do |customer|
          customer.name = params[:customer_name]
          customer.metadata = {
            source: 'chat_widget'
          }
        end
      end

      def serialize_message(message)
        {
          id: message.id,
          content: message.content,
          sender_type: message.sender_type,
          sender_name: message.sender_name,
          created_at: message.created_at.iso8601,
          metadata: message.metadata
        }
      end
    end
  end
end