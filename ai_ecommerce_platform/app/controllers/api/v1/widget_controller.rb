module Api
  module V1
    class WidgetController < BaseController
      skip_before_action :authenticate_api_key!, only: [:config]
      
      def config
        tenant = Tenant.find_by!(api_key: params[:api_key])
        
        render json: {
          tenant: {
            name: tenant.name,
            branding: tenant.branding_config
          },
          agents: tenant.agents.active.map { |agent|
            {
              id: agent.id,
              name: agent.name,
              avatar_url: agent.avatar_url,
              greeting: agent.greeting_message,
              specializations: agent.agent_specializations.pluck(:name)
            }
          },
          widget_settings: {
            position: tenant.widget_config['position'] || 'bottom-right',
            theme: tenant.widget_config['theme'] || 'light',
            primary_color: tenant.widget_config['primary_color'] || '#0066CC',
            auto_open_delay: tenant.widget_config['auto_open_delay']
          }
        }
      end

      def events
        event_data = {
          tenant_id: current_tenant.id,
          session_id: params[:session_id],
          event_type: params[:event_type],
          metadata: params[:metadata],
          timestamp: Time.current
        }
        
        case params[:event_type]
        when 'widget_opened'
          track_widget_opened(event_data)
        when 'widget_closed'
          track_widget_closed(event_data)
        when 'message_sent'
          track_message_sent(event_data)
        when 'page_view'
          track_page_view(event_data)
        end
        
        head :ok
      end

      private

      def track_widget_opened(event_data)
        # Track analytics for widget opens
        Rails.logger.info "Widget opened: #{event_data}"
      end

      def track_widget_closed(event_data)
        # Track analytics for widget closes
        Rails.logger.info "Widget closed: #{event_data}"
      end

      def track_message_sent(event_data)
        # Track analytics for messages
        Rails.logger.info "Message sent: #{event_data}"
      end

      def track_page_view(event_data)
        # Track page views for context
        Rails.logger.info "Page view: #{event_data}"
      end
    end
  end
end