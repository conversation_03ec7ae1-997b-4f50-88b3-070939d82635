module Api
  module V1
    class BaseController < ActionController::API
      before_action :authenticate_api_key!

      private

      def authenticate_api_key!
        api_key = request.headers['X-API-Key']
        
        @tenant = Tenant.find_by(api_key: api_key)
        
        unless @tenant && @tenant.active?
          render json: { error: 'Invalid or inactive API key' }, status: :unauthorized
        end
      end

      def current_tenant
        @tenant
      end
    end
  end
end