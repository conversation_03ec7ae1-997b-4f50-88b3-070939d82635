module Admin
  class TenantsController < BaseController
    before_action :set_tenant, only: [:show, :edit, :update, :destroy]

    def index
      @tenants = Tenant.includes(:users).order(created_at: :desc).page(params[:page])
    end

    def show
      @users = @tenant.users
      @conversations = @tenant.conversations.includes(:customer, :messages).order(created_at: :desc).limit(20)
      @agents = @tenant.agents
    end

    def edit
    end

    def update
      if @tenant.update(tenant_params)
        redirect_to admin_tenant_path(@tenant), notice: 'Tenant was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @tenant.destroy!
      redirect_to admin_tenants_path, notice: 'Tenant was successfully deleted.'
    end

    private

    def set_tenant
      @tenant = Tenant.find(params[:id])
    end

    def tenant_params
      params.require(:tenant).permit(:name, :subdomain, :plan, :status)
    end
  end
end