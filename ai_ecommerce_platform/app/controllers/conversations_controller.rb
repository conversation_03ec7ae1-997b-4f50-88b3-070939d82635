class ConversationsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_conversation, only: [:show]
  
  def index
    @conversations = current_tenant.conversations
                                  .includes(:agent, :messages)
                                  .order(updated_at: :desc)
    @agents = current_tenant.agents.active
  end
  
  def show
    @messages = @conversation.messages
                            .includes(:sender)
                            .order(created_at: :asc)
    @agent = @conversation.agent
  end
  
  def create
    @agent = current_tenant.agents.find(params[:agent_id])
    
    @conversation = current_tenant.conversations.create!(
      agent: @agent,
      metadata: {
        user_id: current_user.id,
        user_email: current_user.email,
        started_at: Time.current
      }
    )
    
    # Create initial greeting message
    greeting = case @agent.kind
    when 'customer_service'
      "Hello! I'm #{@agent.name}, your customer service assistant. How can I help you today?"
    when 'sales'
      "Hi there! I'm #{@agent.name}, your sales consultant. What can I help you find today?"
    when 'technical_support'
      "Hello! I'm #{@agent.name}, your technical support specialist. What technical issue can I help you with?"
    else
      "Hello! I'm #{@agent.name}. How can I assist you today?"
    end
    
    @conversation.messages.create!(
      role: 'assistant',
      content: greeting,
      sender: @agent,
      metadata: { greeting: true }
    )
    
    redirect_to conversation_path(@conversation)
  end
  
  private
  
  def set_conversation
    @conversation = current_tenant.conversations.find(params[:id])
  end
  
  def current_tenant
    @current_tenant ||= current_user.tenants.first || create_demo_tenant
  end
  
  def create_demo_tenant
    tenant = Tenant.create!(
      name: "Demo Company",
      subdomain: "demo-#{SecureRandom.hex(4)}"
    )
    
    current_user.tenant_users.create!(tenant: tenant, role: 'owner')
    
    # Create default agents
    %w[customer_service sales technical_support].each do |kind|
      agent_name = case kind
      when 'customer_service' then 'Emma'
      when 'sales' then 'Alex'
      when 'technical_support' then 'Sam'
      end
      
      tenant.agents.create!(
        name: agent_name,
        kind: kind,
        llm_provider: 'openai',
        llm_model: 'gpt-4o-mini'
      )
    end
    
    tenant
  end
end
