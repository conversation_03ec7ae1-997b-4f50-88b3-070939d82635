class Conversation < ApplicationRecord
  include TenantScoped
  
  # Associations
  has_many :messages, dependent: :destroy
  has_many :agent_handoffs, dependent: :destroy
  has_one :conversation_insight, dependent: :destroy
  belongs_to :assigned_agent, class_name: 'Agent', optional: true
  
  # Validations
  validates :channel, presence: true
  validates :customer_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true
  
  # Status enum
  enum :status, {
    active: 0,
    waiting: 1,
    resolved: 2,
    archived: 3,
    escalated: 4
  }, default: :active
  
  # Channel types
  CHANNELS = %w[web email sms whatsapp facebook instagram api].freeze
  validates :channel, inclusion: { in: CHANNELS }
  
  # Scopes
  scope :active, -> { where(status: :active) }
  scope :recent, -> { order(created_at: :desc) }
  scope :unassigned, -> { where(assigned_agent_id: nil) }
  scope :assigned_to, ->(agent) { where(assigned_agent: agent) }
  
  # Callbacks
  before_create :set_started_at
  after_update :set_ended_at, if: :resolved?
  
  # Methods
  def duration
    return nil unless started_at
    (ended_at || Time.current) - started_at
  end
  
  def last_message
    messages.order(created_at: :desc).first
  end
  
  def customer_messages
    messages.where(sender_type: 'Customer')
  end
  
  def agent_messages
    messages.where(sender_type: 'Agent')
  end
  
  def assign_to(agent)
    update(assigned_agent: agent)
  end
  
  def escalate!(reason = nil)
    update(status: :escalated)
    metadata['escalation_reason'] = reason if reason
    save
  end
  
  private
  
  def set_started_at
    self.started_at ||= Time.current
  end
  
  def set_ended_at
    self.ended_at ||= Time.current
  end
end
