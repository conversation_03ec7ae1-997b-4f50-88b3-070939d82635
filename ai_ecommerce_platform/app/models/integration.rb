class Integration < ApplicationRecord
  include TenantScoped
  
  # Encryption
  encrypts :api_key
  encrypts :api_secret
  
  # Associations
  has_many :products, dependent: :destroy
  has_many :orders, dependent: :destroy
  has_many :customers, dependent: :destroy
  
  # Validations
  validates :platform, presence: true
  validates :api_key, presence: true
  
  # Status enum
  enum :status, {
    active: 0,
    inactive: 1,
    error: 2,
    syncing: 3
  }, default: :inactive
  
  # Platform types
  PLATFORMS = %w[shopify woocommerce magento bigcommerce custom].freeze
  validates :platform, inclusion: { in: PLATFORMS }
  
  # Scopes
  scope :active, -> { where(status: :active) }
  scope :by_platform, ->(platform) { where(platform: platform) }
  
  # Methods
  def connector
    @connector ||= "Integrations::#{platform.camelize}Connector".constantize.new(self)
  rescue NameError
    Integrations::GenericConnector.new(self)
  end
  
  def sync_data!
    update(status: :syncing, last_sync_at: Time.current)
    SyncIntegrationDataJob.perform_later(self)
  end
  
  def test_connection
    connector.test_connection
  rescue => e
    errors.add(:base, "Connection failed: #{e.message}")
    false
  end
  
  def shopify?
    platform == 'shopify'
  end
  
  def woocommerce?
    platform == 'woocommerce'
  end
  
  private
  
  def set_default_settings
    self.settings ||= {
      'sync_products' => true,
      'sync_orders' => true,
      'sync_customers' => true,
      'sync_interval' => 3600, # 1 hour in seconds
      'webhook_enabled' => true
    }
  end
end
