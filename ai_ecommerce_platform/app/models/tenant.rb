class Tenant < ApplicationRecord
  # Associations
  has_many :tenant_users, dependent: :destroy
  has_many :users, through: :tenant_users
  has_many :conversations, dependent: :destroy
  has_many :agents, dependent: :destroy
  has_many :integrations, dependent: :destroy
  has_many :knowledge_sources, dependent: :destroy
  has_many :messages, through: :conversations
  
  # Validations
  validates :name, presence: true
  validates :subdomain, presence: true, uniqueness: true, format: { with: /\A[a-z0-9-]+\z/, message: "only allows lowercase letters, numbers, and hyphens" }
  validates :custom_domain, uniqueness: true, allow_blank: true
  
  # Status enum
  enum :status, {
    active: 0,
    suspended: 1,
    cancelled: 2,
    trial: 3
  }, default: :trial
  
  # Default settings
  after_initialize :set_default_settings
  
  private
  
  def set_default_settings
    self.settings ||= {
      'max_conversations' => 1000,
      'max_agents' => 10,
      'features' => ['chat', 'email', 'analytics'],
      'language' => 'en'
    }
  end
end
