class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :trackable
         
  # Associations
  has_many :tenant_users, dependent: :destroy
  has_many :tenants, through: :tenant_users
  belongs_to :current_tenant, class_name: 'Tenant', optional: true
  
  # Validations
  # Names are optional during registration
  
  # Role enum
  enum :role, {
    super_admin: 0,
    tenant_admin: 1,
    agent: 2,
    viewer: 3
  }, default: :viewer
  
  # Scopes
  scope :for_tenant, ->(tenant) { joins(:tenant_users).where(tenant_users: { tenant_id: tenant.id }) }
  
  # Methods
  def full_name
    if first_name.present? || last_name.present?
      "#{first_name} #{last_name}".strip
    else
      email.split('@').first
    end
  end
  
  def tenant_role(tenant)
    tenant_users.find_by(tenant: tenant)&.role
  end
  
  # Set default preferences
  after_initialize :set_default_preferences
  
  private
  
  def set_default_preferences
    self.preferences ||= {
      'notifications' => true,
      'email_alerts' => true,
      'theme' => 'light'
    }
  end
end
