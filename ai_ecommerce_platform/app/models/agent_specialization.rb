class AgentSpecialization < ApplicationRecord
  belongs_to :agent

  # Virtual attributes for form fields that are stored in configuration
  def name
    configuration&.dig('name') || domain
  end

  def name=(value)
    self.configuration ||= {}
    self.configuration['name'] = value
    self.domain = value if domain.blank?
  end

  def description
    configuration&.dig('description')
  end

  def description=(value)
    self.configuration ||= {}
    self.configuration['description'] = value
  end
end
