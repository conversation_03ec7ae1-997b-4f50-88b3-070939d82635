class Message < ApplicationRecord
  # Associations
  belongs_to :conversation
  belongs_to :sender, polymorphic: true
  
  # Validations
  validates :content, presence: true
  validates :sender_type, presence: true, inclusion: { in: %w[User Agent Customer System] }
  
  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :by_sender_type, ->(type) { where(sender_type: type) }
  scope :with_embeddings, -> { where(embedding_generated: true) }
  
  # Callbacks
  after_create_commit :process_message_async
  after_create_commit :update_conversation_activity
  
  # Delegate
  delegate :tenant, to: :conversation
  
  # Methods
  def from_customer?
    sender_type == 'Customer'
  end
  
  def from_agent?
    sender_type == 'Agent'
  end
  
  def from_system?
    sender_type == 'System'
  end
  
  def display_sender_name
    case sender_type
    when 'Customer'
      conversation.customer_name || 'Customer'
    when 'Agent'
      sender&.name || 'Agent'
    when 'User'
      sender&.full_name || 'User'
    when 'System'
      'System'
    end
  end
  
  private
  
  def process_message_async
    # Queue jobs for message processing
    VectorizeMessageJob.perform_later(self) if from_customer?
    AnalyzeMessageSentimentJob.perform_later(self)
  end
  
  def update_conversation_activity
    conversation.touch(:updated_at)
    conversation.update(status: :active) if conversation.waiting?
  end
end
