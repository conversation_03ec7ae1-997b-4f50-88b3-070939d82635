module TenantScoped
  extend ActiveSupport::Concern

  included do
    belongs_to :tenant
    
    # Set current tenant scope
    default_scope { where(tenant_id: Current.tenant&.id) if Current.tenant }
    
    # Validations
    validates :tenant, presence: true
  end
  
  module ClassMethods
    # Unscoped access when needed
    def unscoped_all
      unscoped.all
    end
    
    # Find records for a specific tenant
    def for_tenant(tenant)
      unscoped.where(tenant: tenant)
    end
  end
end