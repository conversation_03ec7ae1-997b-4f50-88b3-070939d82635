class TenantUser < ApplicationRecord
  belongs_to :tenant
  belongs_to :user
  
  # Validations
  validates :role, presence: true
  validates :user_id, uniqueness: { scope: :tenant_id, message: "is already a member of this tenant" }
  
  # Role options
  ROLES = %w[owner admin agent viewer].freeze
  validates :role, inclusion: { in: ROLES }
  
  # Scopes
  scope :owners, -> { where(role: 'owner') }
  scope :admins, -> { where(role: ['owner', 'admin']) }
  scope :agents, -> { where(role: 'agent') }
  scope :active, -> { joins(:user).where(users: { locked_at: nil }) }
end
