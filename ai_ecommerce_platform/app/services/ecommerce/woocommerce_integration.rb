module Ecommerce
  class WoocommerceIntegration < BaseIntegration
    def sync_products
      products = []
      page = 1
      
      loop do
        response = client.get('products', page: page, per_page: 100)
        break if response.empty?
        
        response.each do |product_data|
          product = integration.products.find_or_initialize_by(
            external_id: product_data['id'].to_s
          )
          
          product.update!(
            name: product_data['name'],
            description: product_data['description'],
            price: product_data['price'].to_f,
            sku: product_data['sku'],
            inventory_count: product_data['stock_quantity'],
            metadata: product_data
          )
          
          products << product
        end
        
        page += 1
      end
      
      { success: true, synced_count: products.count }
    rescue => e
      handle_api_error(e)
    end

    def sync_orders
      orders = []
      page = 1
      
      loop do
        response = client.get('orders', page: page, per_page: 100)
        break if response.empty?
        
        response.each do |order_data|
          order = integration.orders.find_or_initialize_by(
            external_id: order_data['id'].to_s
          )
          
          customer = find_or_create_customer(order_data['billing']) if order_data['billing']
          
          order.update!(
            customer: customer,
            status: order_data['status'],
            total_amount: order_data['total'].to_f,
            order_number: order_data['number'],
            metadata: order_data
          )
          
          orders << order
        end
        
        page += 1
      end
      
      { success: true, synced_count: orders.count }
    rescue => e
      handle_api_error(e)
    end

    def sync_customers
      customers = []
      page = 1
      
      loop do
        response = client.get('customers', page: page, per_page: 100)
        break if response.empty?
        
        response.each do |customer_data|
          customer = find_or_create_customer(customer_data)
          customers << customer
        end
        
        page += 1
      end
      
      { success: true, synced_count: customers.count }
    rescue => e
      handle_api_error(e)
    end

    def fetch_product(product_id)
      client.get("products/#{product_id}")
    rescue => e
      handle_api_error(e)
    end

    def fetch_order(order_id)
      client.get("orders/#{order_id}")
    rescue => e
      handle_api_error(e)
    end

    def fetch_customer(customer_id)
      client.get("customers/#{customer_id}")
    rescue => e
      handle_api_error(e)
    end

    def webhook_handler(payload)
      event = payload['event']
      resource = payload['resource']
      data = payload['data']
      
      case event
      when 'product.created', 'product.updated'
        sync_single_product(data)
      when 'order.created', 'order.updated'
        sync_single_order(data)
      when 'customer.created', 'customer.updated'
        sync_single_customer(data)
      end
      
      { success: true }
    rescue => e
      handle_api_error(e)
    end

    def test_connection
      client.get('system_status')
      { success: true, message: 'Connection successful' }
    rescue => e
      handle_api_error(e)
    end

    protected

    def build_client
      WooCommerce::API.new(
        integration.config['store_url'],
        integration.config['consumer_key'],
        integration.config['consumer_secret'],
        {
          version: 'wc/v3',
          verify_ssl: true
        }
      )
    end

    private

    def find_or_create_customer(customer_data)
      email = customer_data['email'] || customer_data['billing_email']
      return nil unless email
      
      customer = integration.customers.find_or_initialize_by(
        external_id: customer_data['id'].to_s
      )
      
      customer.update!(
        email: email,
        name: "#{customer_data['first_name']} #{customer_data['last_name']}".strip,
        phone: customer_data['phone'] || customer_data['billing_phone'],
        metadata: customer_data
      )
      
      customer
    end
  end
end