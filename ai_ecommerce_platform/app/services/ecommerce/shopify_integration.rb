module Ecommerce
  class ShopifyIntegration < BaseIntegration
    def sync_products
      response = client.get('products')
      
      response['products'].each do |product_data|
        product = integration.products.find_or_initialize_by(
          external_id: product_data['id'].to_s
        )
        
        product.update!(
          name: product_data['title'],
          description: product_data['body_html'],
          price: product_data['variants'].first['price'].to_f,
          sku: product_data['variants'].first['sku'],
          inventory_count: product_data['variants'].first['inventory_quantity'],
          metadata: product_data
        )
      end
      
      { success: true, synced_count: response['products'].count }
    rescue => e
      handle_api_error(e)
    end

    def sync_orders
      response = client.get('orders', status: 'any')
      
      response['orders'].each do |order_data|
        order = integration.orders.find_or_initialize_by(
          external_id: order_data['id'].to_s
        )
        
        customer = find_or_create_customer(order_data['customer']) if order_data['customer']
        
        order.update!(
          customer: customer,
          status: order_data['financial_status'],
          total_amount: order_data['total_price'].to_f,
          order_number: order_data['order_number'],
          metadata: order_data
        )
      end
      
      { success: true, synced_count: response['orders'].count }
    rescue => e
      handle_api_error(e)
    end

    def sync_customers
      response = client.get('customers')
      
      response['customers'].each do |customer_data|
        find_or_create_customer(customer_data)
      end
      
      { success: true, synced_count: response['customers'].count }
    rescue => e
      handle_api_error(e)
    end

    def fetch_product(product_id)
      response = client.get("products/#{product_id}")
      response['product']
    rescue => e
      handle_api_error(e)
    end

    def fetch_order(order_id)
      response = client.get("orders/#{order_id}")
      response['order']
    rescue => e
      handle_api_error(e)
    end

    def fetch_customer(customer_id)
      response = client.get("customers/#{customer_id}")
      response['customer']
    rescue => e
      handle_api_error(e)
    end

    def webhook_handler(payload)
      topic = payload['topic']
      data = payload['data']
      
      case topic
      when 'products/create', 'products/update'
        sync_product(data)
      when 'orders/create', 'orders/updated'
        sync_order(data)
      when 'customers/create', 'customers/update'
        sync_customer(data)
      end
      
      { success: true }
    rescue => e
      handle_api_error(e)
    end

    def test_connection
      client.get('shop')
      { success: true, message: 'Connection successful' }
    rescue => e
      handle_api_error(e)
    end

    protected

    def build_client
      ShopifyAPI::Client.new(
        shop_domain: integration.config['shop_domain'],
        api_key: integration.config['api_key'],
        api_secret: integration.config['api_secret'],
        access_token: integration.config['access_token']
      )
    end

    private

    def find_or_create_customer(customer_data)
      customer = integration.customers.find_or_initialize_by(
        external_id: customer_data['id'].to_s
      )
      
      customer.update!(
        email: customer_data['email'],
        name: "#{customer_data['first_name']} #{customer_data['last_name']}".strip,
        phone: customer_data['phone'],
        metadata: customer_data
      )
      
      customer
    end
  end
end