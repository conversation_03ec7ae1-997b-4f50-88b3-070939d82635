module Ecommerce
  class IntegrationFactory
    INTEGRATIONS = {
      'shopify' => ShopifyIntegration,
      'woocommerce' => WoocommerceIntegration
    }.freeze

    def self.create(integration)
      integration_class = INTEGRATIONS[integration.platform]
      
      raise ArgumentError, "Unsupported platform: #{integration.platform}" unless integration_class
      
      integration_class.new(integration)
    end
  end
end