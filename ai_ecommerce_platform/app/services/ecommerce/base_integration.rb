module Ecommerce
  class BaseIntegration
    attr_reader :integration, :client

    def initialize(integration)
      @integration = integration
      @client = build_client
    end

    def sync_products
      raise NotImplementedError, "#{self.class} must implement #sync_products"
    end

    def sync_orders
      raise NotImplementedError, "#{self.class} must implement #sync_orders"
    end

    def sync_customers
      raise NotImplementedError, "#{self.class} must implement #sync_customers"
    end

    def fetch_product(product_id)
      raise NotImplementedError, "#{self.class} must implement #fetch_product"
    end

    def fetch_order(order_id)
      raise NotImplementedError, "#{self.class} must implement #fetch_order"
    end

    def fetch_customer(customer_id)
      raise NotImplementedError, "#{self.class} must implement #fetch_customer"
    end

    def webhook_handler(payload)
      raise NotImplementedError, "#{self.class} must implement #webhook_handler"
    end

    def test_connection
      raise NotImplementedError, "#{self.class} must implement #test_connection"
    end

    protected

    def build_client
      raise NotImplementedError, "#{self.class} must implement #build_client"
    end

    def handle_api_error(error)
      Rails.logger.error "#{self.class} API Error: #{error.message}"
      { success: false, error: error.message }
    end
  end
end