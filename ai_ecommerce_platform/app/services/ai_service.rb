class AiService
  include ActiveSupport::Configurable

  config_accessor :default_provider, :default_model, :default_temperature

  def initialize(tenant: nil, agent: nil)
    @tenant = tenant
    @agent = agent
    @provider = determine_provider
    @model = determine_model
  end

  def chat(messages, **options)
    case @provider
    when :openai
      chat_with_openai(messages, **options)
    when :anthropic
      chat_with_anthropic(messages, **options)
    when :google
      chat_with_google(messages, **options)
    when :deepseek
      chat_with_deepseek(messages, **options)
    when :openrouter
      chat_with_openrouter(messages, **options)
    when :ollama
      chat_with_ollama(messages, **options)
    when :bedrock
      chat_with_bedrock(messages, **options)
    else
      raise "Unsupported provider: #{@provider}"
    end
  end

  def embeddings(text, **options)
    case @provider
    when :openai
      RubyLLM.embeddings(text, model: "text-embedding-3-small", **options)
    when :google
      RubyLLM.embeddings(text, provider: :google, model: "text-embedding-004", **options)
    when :bedrock
      RubyLLM.embeddings(text, provider: :bedrock, model: "amazon.titan-embed-text-v1", **options)
    else
      # Fallback to OpenAI embeddings if provider doesn't support embeddings
      RubyLLM.embeddings(text, provider: :openai, model: "text-embedding-3-small", **options)
    end
  end

  def stream_chat(messages, **options, &block)
    options[:stream] = true
    
    case @provider
    when :openai
      RubyLLM.chat(messages, provider: :openai, model: @model, **options, &block)
    when :anthropic
      RubyLLM.chat(messages, provider: :anthropic, model: @model, **options, &block)
    when :google
      RubyLLM.chat(messages, provider: :google, model: @model, **options, &block)
    when :deepseek
      RubyLLM.chat(messages, provider: :deepseek, model: @model, **options, &block)
    else
      raise "Streaming not supported for provider: #{@provider}"
    end
  end

  def available_models
    case @provider
    when :openai
      ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"]
    when :anthropic
      ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
    when :google
      ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"]
    when :deepseek
      ["deepseek-chat", "deepseek-coder"]
    when :openrouter
      # OpenRouter provides access to many models
      ["openai/gpt-4o", "anthropic/claude-3-opus", "google/gemini-pro-1.5", "meta-llama/llama-3.1-405b-instruct"]
    when :ollama
      # These would be dynamically fetched from local Ollama instance
      ["llama3.1", "mistral", "phi3", "qwen2.5"]
    when :bedrock
      ["anthropic.claude-3-opus-20240229-v1:0", "anthropic.claude-3-sonnet-20240229-v1:0", "amazon.titan-text-express-v1"]
    else
      []
    end
  end

  private

  def determine_provider
    # Priority: Agent > Tenant > Config
    if @agent&.llm_provider.present?
      @agent.llm_provider.to_sym
    elsif @tenant&.settings&.dig("default_llm_provider").present?
      @tenant.settings["default_llm_provider"].to_sym
    else
      (config.default_provider || :openai).to_sym
    end
  end

  def determine_model
    # Priority: Agent > Tenant > Config > Default for provider
    if @agent&.llm_model.present?
      @agent.llm_model
    elsif @tenant&.settings&.dig("default_llm_model").present?
      @tenant.settings["default_llm_model"]
    elsif config.default_model.present?
      config.default_model
    else
      default_model_for_provider
    end
  end

  def default_model_for_provider
    case @provider
    when :openai then "gpt-4o"
    when :anthropic then "claude-3-sonnet-20240229"
    when :google then "gemini-1.5-flash"
    when :deepseek then "deepseek-chat"
    when :openrouter then "openai/gpt-4o-mini"
    when :ollama then "llama3.1"
    when :bedrock then "anthropic.claude-3-sonnet-20240229-v1:0"
    else "gpt-4o"
    end
  end

  def chat_with_openai(messages, **options)
    RubyLLM.chat(messages, provider: :openai, model: @model, **options)
  end

  def chat_with_anthropic(messages, **options)
    RubyLLM.chat(messages, provider: :anthropic, model: @model, **options)
  end

  def chat_with_google(messages, **options)
    # For Google, we'll need to use a different approach or custom implementation
    # since ruby_llm might not support all providers out of the box
    raise NotImplementedError, "Google Gemini integration pending implementation"
  end

  def chat_with_deepseek(messages, **options)
    # DeepSeek uses OpenAI-compatible API
    options[:api_base] = "https://api.deepseek.com/v1"
    options[:api_key] = Rails.application.credentials.dig(:deepseek, :api_key) || ENV['DEEPSEEK_API_KEY']
    RubyLLM.chat(messages, provider: :openai, model: @model, **options)
  end

  def chat_with_openrouter(messages, **options)
    # OpenRouter also uses OpenAI-compatible API
    options[:api_base] = "https://openrouter.ai/api/v1"
    options[:api_key] = Rails.application.credentials.dig(:openrouter, :api_key) || ENV['OPENROUTER_API_KEY']
    RubyLLM.chat(messages, provider: :openai, model: @model, **options)
  end

  def chat_with_ollama(messages, **options)
    # Ollama for local models
    options[:api_base] = Rails.application.credentials.dig(:ollama, :base_url) || ENV['OLLAMA_BASE_URL'] || "http://localhost:11434"
    RubyLLM.chat(messages, provider: :openai, model: @model, **options)
  end

  def chat_with_bedrock(messages, **options)
    # AWS Bedrock would need custom implementation
    raise NotImplementedError, "AWS Bedrock integration pending implementation"
  end
end