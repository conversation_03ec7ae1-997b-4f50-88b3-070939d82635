module Ai
  class TestService
    def initialize(agent)
      @agent = agent
    end

    def test_response(message)
      # Use the appropriate AI provider based on agent configuration
      case @agent.model_provider
      when 'openai'
        test_with_openai(message)
      when 'anthropic'
        test_with_anthropic(message)
      when 'google'
        test_with_google(message)
      else
        fallback_response(message)
      end
    end

    private

    def test_with_openai(message)
      client = OpenAI::Client.new(access_token: ENV['OPENAI_API_KEY'])
      
      response = client.chat(
        parameters: {
          model: @agent.model_name || 'gpt-4',
          messages: [
            {
              role: 'system',
              content: build_system_prompt
            },
            {
              role: 'user',
              content: message
            }
          ],
          temperature: @agent.temperature || 0.7,
          max_tokens: @agent.max_tokens || 500
        }
      )
      
      response.dig('choices', 0, 'message', 'content')
    rescue => e
      "Error: #{e.message}"
    end

    def test_with_anthropic(message)
      # Placeholder for Anthropic implementation
      fallback_response(message)
    end

    def test_with_google(message)
      # Placeholder for Google implementation
      fallback_response(message)
    end

    def build_system_prompt
      base_prompt = @agent.system_prompt || "You are a helpful AI assistant."
      
      prompt_parts = [base_prompt]
      
      if @agent.personality.present?
        prompt_parts << "Your personality: #{@agent.personality}"
      end
      
      if @agent.response_style.present?
        prompt_parts << "Response style: #{@agent.response_style}"
      end
      
      if @agent.agent_specializations.any?
        specializations = @agent.agent_specializations.pluck(:name).join(', ')
        prompt_parts << "You specialize in: #{specializations}"
      end
      
      prompt_parts.join("\n\n")
    end

    def fallback_response(message)
      responses = [
        "I understand you're asking about '#{message}'. Based on my configuration as #{@agent.name}, I would provide helpful information about this topic.",
        "Thank you for your message. As #{@agent.name}, I'm here to help with #{@agent.role} inquiries. How can I assist you further?",
        "I've received your message: '#{message}'. Let me help you with that based on my expertise in #{@agent.agent_specializations.pluck(:name).join(', ')}."
      ]
      
      responses.sample
    end
  end
end