class ChatCompletionService
  attr_reader :conversation, :agent, :tenant

  def initialize(conversation:, agent: nil)
    @conversation = conversation
    @agent = agent || conversation.agent
    @tenant = conversation.tenant
  end

  def process_message(content:, metadata: {})
    # Create user message
    user_message = create_user_message(content, metadata)
    
    # Get conversation context
    messages = build_message_context
    
    # Generate AI response
    ai_response = generate_ai_response(messages)
    
    # Create assistant message
    assistant_message = create_assistant_message(ai_response)
    
    # Check if handoff is needed
    check_for_handoff(ai_response, assistant_message)
    
    # Generate insights if needed
    generate_insights if should_generate_insights?
    
    assistant_message
  rescue StandardError => e
    Rails.logger.error "ChatCompletionService error: #{e.message}"
    create_error_message(e)
  end

  def process_message_stream(content:, metadata: {}, &block)
    # Create user message
    user_message = create_user_message(content, metadata)
    
    # Get conversation context
    messages = build_message_context
    
    # Create placeholder for assistant message
    assistant_message = conversation.messages.create!(
      role: 'assistant',
      content: '',
      sender: agent,
      metadata: { streaming: true }
    )
    
    # Stream AI response
    full_response = ""
    
    agent.generate_response_stream(messages) do |chunk|
      full_response += chunk
      assistant_message.update!(content: full_response)
      block.call(chunk, assistant_message) if block_given?
    end
    
    # Update final message
    assistant_message.update!(
      content: full_response,
      metadata: assistant_message.metadata.merge(streaming: false)
    )
    
    # Check if handoff is needed
    check_for_handoff({ content: full_response }, assistant_message)
    
    assistant_message
  rescue StandardError => e
    Rails.logger.error "ChatCompletionService streaming error: #{e.message}"
    create_error_message(e)
  end

  private

  def create_user_message(content, metadata)
    conversation.messages.create!(
      role: 'user',
      content: content,
      metadata: metadata
    )
  end

  def create_assistant_message(ai_response)
    conversation.messages.create!(
      role: 'assistant',
      content: ai_response[:content],
      sender: agent,
      metadata: {
        finish_reason: ai_response[:finish_reason],
        usage: ai_response[:usage],
        model: ai_response.dig(:raw, 'model')
      }
    )
  end

  def create_error_message(error)
    conversation.messages.create!(
      role: 'system',
      content: "I apologize, but I encountered an error processing your request. Please try again.",
      metadata: {
        error: error.message,
        error_class: error.class.name
      }
    )
  end

  def build_message_context
    # Get recent messages for context
    recent_messages = conversation.messages
                                 .order(created_at: :desc)
                                 .limit(20)
                                 .reverse
    
    # Add system prompt based on agent type
    system_prompt = build_system_prompt
    
    # Format messages for AI
    messages = [{ role: 'system', content: system_prompt }]
    
    recent_messages.each do |msg|
      messages << {
        role: msg.role,
        content: msg.content,
        name: msg.sender&.name
      }.compact
    end
    
    messages
  end

  def build_system_prompt
    base_prompt = "You are #{agent.name}, a #{agent.kind.humanize.downcase} AI assistant for #{tenant.name}."
    
    # Add agent-specific instructions
    case agent.kind
    when 'customer_service'
      base_prompt += " Help customers with their inquiries, orders, and issues. Be helpful, empathetic, and professional."
    when 'sales'
      base_prompt += " Assist customers with product recommendations and purchases. Be informative and persuasive but not pushy."
    when 'technical_support'
      base_prompt += " Help users troubleshoot technical issues. Be clear, patient, and thorough in your explanations."
    end
    
    # Add capabilities
    if agent.capabilities.present?
      base_prompt += " Your capabilities include: #{agent.capabilities.join(', ')}."
    end
    
    # Add custom instructions from settings
    if agent.settings['custom_instructions'].present?
      base_prompt += " #{agent.settings['custom_instructions']}"
    end
    
    base_prompt
  end

  def generate_ai_response(messages)
    options = {
      temperature: agent.settings['temperature'] || 0.7,
      max_tokens: agent.settings['max_tokens'] || 1000
    }
    
    agent.generate_response(messages, **options)
  end

  def check_for_handoff(ai_response, assistant_message)
    # Check if the response indicates a need for handoff
    handoff_indicators = [
      'transfer to human',
      'speak to a person',
      'need human assistance',
      'escalate this issue',
      'beyond my capabilities'
    ]
    
    response_text = ai_response[:content].downcase
    
    if handoff_indicators.any? { |indicator| response_text.include?(indicator) }
      create_handoff_request(assistant_message)
    end
  end

  def create_handoff_request(message)
    # Find appropriate agent or mark for human handoff
    target_agent = find_target_agent_for_handoff
    
    AgentHandoff.create!(
      conversation: conversation,
      from_agent: agent,
      to_agent: target_agent,
      reason: 'escalation_required',
      message: message,
      metadata: {
        detected_at: Time.current,
        auto_detected: true
      }
    )
  end

  def find_target_agent_for_handoff
    # Logic to find the best agent for handoff
    # This could be based on availability, specialization, etc.
    Agent.active
         .where.not(id: agent.id)
         .where(tenant: tenant)
         .find(&:available?)
  end

  def should_generate_insights?
    # Generate insights every 10 messages or if conversation is being closed
    conversation.messages.count % 10 == 0 || conversation.closed?
  end

  def generate_insights
    GenerateConversationInsightsJob.perform_later(conversation)
  end
end