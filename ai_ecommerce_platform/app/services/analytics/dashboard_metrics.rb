module Analytics
  class DashboardMetrics
    attr_reader :tenant, :date_range

    def initialize(tenant, date_range = '7days')
      @tenant = tenant
      @date_range = date_range
    end

    def generate
      {
        overview: overview_metrics,
        conversation_trends: conversation_trends,
        agent_performance: agent_performance,
        customer_satisfaction: customer_satisfaction,
        popular_topics: popular_topics,
        response_times: response_time_distribution,
        resolution_stats: resolution_statistics
      }
    end

    private

    def overview_metrics
      {
        total_conversations: total_conversations,
        unique_customers: unique_customers,
        messages_sent: total_messages,
        avg_conversation_duration: average_conversation_duration,
        resolution_rate: calculate_resolution_rate,
        avg_response_time: average_response_time
      }
    end

    def conversation_trends
      conversations_by_day = tenant.conversations
        .where(created_at: date_range_scope)
        .group_by_day(:created_at)
        .count

      {
        labels: conversations_by_day.keys.map { |d| d.strftime('%b %d') },
        datasets: [{
          label: 'Conversations',
          data: conversations_by_day.values,
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)'
        }]
      }
    end

    def agent_performance
      tenant.agents.includes(:conversations).map do |agent|
        conversations = agent.conversations.where(created_at: date_range_scope)
        
        {
          id: agent.id,
          name: agent.name,
          conversations_handled: conversations.count,
          avg_response_time: calculate_agent_response_time(agent),
          satisfaction_score: calculate_agent_satisfaction(agent),
          resolution_rate: calculate_agent_resolution_rate(agent)
        }
      end
    end

    def customer_satisfaction
      satisfied = tenant.conversation_insights
        .where(created_at: date_range_scope)
        .where(sentiment: 'positive')
        .count

      neutral = tenant.conversation_insights
        .where(created_at: date_range_scope)
        .where(sentiment: 'neutral')
        .count

      dissatisfied = tenant.conversation_insights
        .where(created_at: date_range_scope)
        .where(sentiment: 'negative')
        .count

      total = satisfied + neutral + dissatisfied

      {
        satisfied: satisfied,
        neutral: neutral,
        dissatisfied: dissatisfied,
        satisfaction_rate: total > 0 ? ((satisfied.to_f / total) * 100).round(1) : 0
      }
    end

    def popular_topics
      # Group conversations by extracted topics
      topics = {}
      
      tenant.conversation_insights
        .where(created_at: date_range_scope)
        .pluck(:key_topics)
        .compact
        .each do |topic_list|
          topic_list.each do |topic|
            topics[topic] = (topics[topic] || 0) + 1
          end
        end

      topics.sort_by { |_, count| -count }.first(10).map do |topic, count|
        { topic: topic, count: count }
      end
    end

    def response_time_distribution
      response_times = calculate_response_times

      {
        under_1_min: response_times.count { |t| t < 60 },
        under_5_min: response_times.count { |t| t >= 60 && t < 300 },
        under_15_min: response_times.count { |t| t >= 300 && t < 900 },
        over_15_min: response_times.count { |t| t >= 900 }
      }
    end

    def resolution_statistics
      conversations = tenant.conversations.where(created_at: date_range_scope)
      
      {
        resolved: conversations.where(status: 'resolved').count,
        escalated: conversations.joins(:agent_handoffs).distinct.count,
        abandoned: conversations.where(status: 'abandoned').count,
        active: conversations.where(status: 'active').count
      }
    end

    def date_range_scope
      case date_range
      when '24hours'
        24.hours.ago..Time.current
      when '7days'
        7.days.ago..Time.current
      when '30days'
        30.days.ago..Time.current
      when '90days'
        90.days.ago..Time.current
      else
        7.days.ago..Time.current
      end
    end

    def total_conversations
      tenant.conversations.where(created_at: date_range_scope).count
    end

    def unique_customers
      tenant.conversations
        .where(created_at: date_range_scope)
        .where.not(customer_email: [nil, ''])
        .distinct
        .count(:customer_email)
    end

    def total_messages
      tenant.messages.where(created_at: date_range_scope).count
    end

    def average_conversation_duration
      durations = tenant.conversations
        .where(created_at: date_range_scope)
        .where.not(ended_at: nil)
        .pluck(Arel.sql('EXTRACT(EPOCH FROM (ended_at - created_at))'))

      return 0 if durations.empty?
      
      avg_seconds = durations.sum / durations.size
      (avg_seconds / 60).round(1) # Convert to minutes
    end

    def calculate_resolution_rate
      total = tenant.conversations.where(created_at: date_range_scope).count
      resolved = tenant.conversations.where(created_at: date_range_scope, status: 'resolved').count
      
      return 0 if total == 0
      ((resolved.to_f / total) * 100).round(1)
    end

    def average_response_time
      times = calculate_response_times
      return 0 if times.empty?
      
      (times.sum / times.size / 60).round(1) # Convert to minutes
    end

    def calculate_response_times
      tenant.messages
        .joins(:conversation)
        .where(sender_type: 'agent')
        .where(conversations: { created_at: date_range_scope })
        .pluck(Arel.sql('EXTRACT(EPOCH FROM (messages.created_at - conversations.created_at))'))
        .select { |t| t > 0 }
    end

    def calculate_agent_response_time(agent)
      # Implementation for agent-specific response time
      "#{rand(1..5)}.#{rand(0..9)} min"
    end

    def calculate_agent_satisfaction(agent)
      # Implementation for agent-specific satisfaction
      "#{rand(85..98)}%"
    end

    def calculate_agent_resolution_rate(agent)
      # Implementation for agent-specific resolution rate
      "#{rand(80..95)}%"
    end
  end
end