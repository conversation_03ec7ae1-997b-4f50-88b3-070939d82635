module KnowledgeBase
  class DocumentProcessor
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200

    def initialize(knowledge_source)
      @knowledge_source = knowledge_source
    end

    def process
      content = extract_content
      chunks = create_chunks(content)
      embeddings = generate_embeddings(chunks)
      
      store_embeddings(chunks, embeddings)
      
      @knowledge_source.update!(
        status: 'processed',
        processed_at: Time.current,
        chunk_count: chunks.size
      )
      
      { success: true, chunks_created: chunks.size }
    rescue => e
      @knowledge_source.update!(status: 'failed', error_message: e.message)
      { success: false, error: e.message }
    end

    private

    def extract_content
      case @knowledge_source.source_type
      when 'file'
        extract_file_content
      when 'url'
        extract_url_content
      when 'text'
        @knowledge_source.content
      else
        raise "Unsupported source type: #{@knowledge_source.source_type}"
      end
    end

    def extract_file_content
      file = @knowledge_source.file_attachment
      
      case file.content_type
      when 'application/pdf'
        extract_pdf_content(file)
      when 'text/plain', 'text/markdown'
        file.download
      when 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        extract_docx_content(file)
      else
        raise "Unsupported file type: #{file.content_type}"
      end
    end

    def extract_url_content
      response = HTTParty.get(@knowledge_source.url)
      Nokogiri::HTML(response.body).text.squish
    end

    def create_chunks(content)
      chunks = []
      words = content.split
      
      (0...words.length).step(CHUNK_SIZE - CHUNK_OVERLAP) do |i|
        chunk_words = words[i...(i + CHUNK_SIZE)]
        chunks << chunk_words.join(' ')
        
        break if i + CHUNK_SIZE >= words.length
      end
      
      chunks
    end

    def generate_embeddings(chunks)
      client = OpenAI::Client.new(access_token: ENV['OPENAI_API_KEY'])
      
      chunks.map do |chunk|
        response = client.embeddings(
          parameters: {
            model: 'text-embedding-3-small',
            input: chunk
          }
        )
        
        response['data'][0]['embedding']
      end
    end

    def store_embeddings(chunks, embeddings)
      chunks.each_with_index do |chunk, index|
        @knowledge_source.embeddings.create!(
          content: chunk,
          embedding: embeddings[index],
          chunk_index: index
        )
      end
    end

    def extract_pdf_content(file)
      # Implementation would use a PDF extraction library
      # For now, returning placeholder
      "PDF content extraction not yet implemented"
    end

    def extract_docx_content(file)
      # Implementation would use a DOCX extraction library
      # For now, returning placeholder
      "DOCX content extraction not yet implemented"
    end
  end
end