module KnowledgeBase
  class VectorSearch
    def initialize(tenant)
      @tenant = tenant
      @client = OpenAI::Client.new(access_token: ENV['OPENAI_API_KEY'])
    end

    def search(query, limit: 5, threshold: 0.7)
      query_embedding = generate_query_embedding(query)
      
      results = Embedding
        .joins(:knowledge_source)
        .where(knowledge_source: { tenant: @tenant })
        .select(
          "embeddings.*",
          "knowledge_sources.name as source_name",
          "(embedding <-> '#{query_embedding.to_s}') as distance"
        )
        .where("1 - (embedding <-> '#{query_embedding.to_s}') > ?", threshold)
        .order("distance")
        .limit(limit)
      
      format_results(results)
    end

    def semantic_search(query, filters: {}, limit: 5)
      query_embedding = generate_query_embedding(query)
      
      scope = Embedding.joins(:knowledge_source).where(knowledge_source: { tenant: @tenant })
      
      if filters[:source_types].present?
        scope = scope.where(knowledge_source: { source_type: filters[:source_types] })
      end
      
      if filters[:created_after].present?
        scope = scope.where('knowledge_sources.created_at > ?', filters[:created_after])
      end
      
      results = scope
        .select(
          "embeddings.*",
          "knowledge_sources.name as source_name",
          "knowledge_sources.source_type",
          "(embedding <-> '#{query_embedding.to_s}') as distance"
        )
        .order("distance")
        .limit(limit)
      
      format_results(results)
    end

    def find_similar(embedding_id, limit: 5)
      embedding = Embedding.find(embedding_id)
      
      results = Embedding
        .joins(:knowledge_source)
        .where(knowledge_source: { tenant: @tenant })
        .where.not(id: embedding_id)
        .select(
          "embeddings.*",
          "knowledge_sources.name as source_name",
          "(embedding <-> '#{embedding.embedding.to_s}') as distance"
        )
        .order("distance")
        .limit(limit)
      
      format_results(results)
    end

    private

    def generate_query_embedding(query)
      response = @client.embeddings(
        parameters: {
          model: 'text-embedding-3-small',
          input: query
        }
      )
      
      response['data'][0]['embedding']
    end

    def format_results(results)
      results.map do |result|
        {
          id: result.id,
          content: result.content,
          source_name: result.source_name,
          source_type: result.try(:source_type),
          similarity_score: 1 - result.distance,
          chunk_index: result.chunk_index
        }
      end
    end
  end
end