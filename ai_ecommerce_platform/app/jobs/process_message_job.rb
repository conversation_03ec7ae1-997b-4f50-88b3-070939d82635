class ProcessMessageJob < ApplicationJob
  queue_as :default

  def perform(user_message)
    conversation = user_message.conversation
    chat_service = ChatCompletionService.new(conversation: conversation)
    
    # Process the message and get AI response
    assistant_message = chat_service.process_message(
      content: user_message.content,
      metadata: user_message.metadata
    )
    
    # Broadcast the AI response via ActionCable
    ConversationChannel.broadcast_to(
      conversation,
      {
        type: 'message',
        message: render_message(assistant_message)
      }
    )
  rescue StandardError => e
    Rails.logger.error "ProcessMessageJob error: #{e.message}"
    
    # Create error message
    error_message = conversation.messages.create!(
      role: 'system',
      content: "I apologize, but I encountered an error. Please try again.",
      metadata: { error: e.message }
    )
    
    # Broadcast error message
    ConversationChannel.broadcast_to(
      conversation,
      {
        type: 'message',
        message: render_message(error_message)
      }
    )
  end
  
  private
  
  def render_message(message)
    ApplicationController.render(
      partial: 'messages/message',
      locals: { message: message }
    )
  end
end
