class ProcessKnowledgeSourceJob < ApplicationJob
  queue_as :default

  def perform(knowledge_source_id)
    knowledge_source = KnowledgeSource.find(knowledge_source_id)
    
    processor = KnowledgeBase::DocumentProcessor.new(knowledge_source)
    result = processor.process
    
    if result[:success]
      Rails.logger.info "Successfully processed knowledge source #{knowledge_source_id}: #{result[:chunks_created]} chunks created"
    else
      Rails.logger.error "Failed to process knowledge source #{knowledge_source_id}: #{result[:error]}"
    end
  end
end