class SyncEcommerceDataJob < ApplicationJob
  queue_as :default

  def perform(integration_id, sync_type = 'all')
    integration = Integration.find(integration_id)
    service = Ecommerce::IntegrationFactory.create(integration)
    
    results = {}
    
    case sync_type
    when 'all'
      results[:products] = service.sync_products
      results[:orders] = service.sync_orders
      results[:customers] = service.sync_customers
    when 'products'
      results[:products] = service.sync_products
    when 'orders'
      results[:orders] = service.sync_orders
    when 'customers'
      results[:customers] = service.sync_customers
    end
    
    integration.update!(
      last_synced_at: Time.current,
      sync_status: 'completed',
      sync_results: results
    )
    
    Rails.logger.info "Successfully synced #{sync_type} for integration #{integration_id}: #{results}"
  rescue => e
    integration.update!(
      sync_status: 'failed',
      sync_error: e.message
    )
    
    Rails.logger.error "Failed to sync #{sync_type} for integration #{integration_id}: #{e.message}"
    raise
  end
end