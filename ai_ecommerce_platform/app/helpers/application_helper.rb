module ApplicationHelper
  def conversation_status_color(status)
    case status
    when 'active'
      'bg-green-100 text-green-800'
    when 'resolved'
      'bg-blue-100 text-blue-800'
    when 'escalated'
      'bg-yellow-100 text-yellow-800'
    when 'abandoned'
      'bg-red-100 text-red-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end
  
  def agent_icon(agent)
    icons = {
      'customer_service' => '🎧',
      'sales' => '📊',
      'marketing' => '📊',
      'hr' => '👥',
      'it_support' => '💻',
      'research' => '🔍',
      'creative' => '✍️',
      'code' => '⚡',
      'predictive' => '🔮'
    }
    
    # Try to match agent type/kind
    return '🤖' unless agent&.name.present?
    
    icon_key = agent.kind&.downcase || agent.name.downcase.gsub(/[^a-z]/, '_')
    icons.find { |k, v| icon_key.include?(k) }&.last || '🤖'
  end
  
  def default_agent_description(agent)
    descriptions = {
      'customer_service' => '24/7 customer support with context awareness',
      'sales' => 'Lead generation and sales automation',
      'marketing' => 'Campaign management and analytics',
      'hr' => 'Employee support and HR automation',
      'it_support' => 'Technical support and system management',
      'research' => 'Data analysis and business intelligence',
      'creative' => 'Content creation and copywriting',
      'code' => 'Development support and code review',
      'predictive' => 'Autonomous predictions and insights'
    }
    
    # Try to match agent type/kind
    return 'AI-powered assistant' unless agent
    desc_key = agent.kind&.downcase || agent.name&.downcase&.gsub(/[^a-z]/, '_')
    return 'AI-powered assistant' unless desc_key
    
    descriptions.find { |k, v| desc_key.include?(k) }&.last || 'AI-powered assistant'
  end
end
