module Dash<PERSON><PERSON><PERSON><PERSON>
  def conversation_status_color(status)
    case status.to_s
    when 'active'
      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
    when 'waiting'
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
    when 'resolved'
      'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
    when 'escalated'
      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
    else
      'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
    end
  end
  
  def metric_change_class(value)
    if value.positive?
      'text-green-600 dark:text-green-400'
    elsif value.negative?
      'text-red-600 dark:text-red-400'
    else
      'text-gray-600 dark:text-gray-400'
    end
  end
  
  def metric_icon(type)
    case type
    when :conversations
      '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>'.html_safe
    when :resolved
      '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>'.html_safe
    when :time
      '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>'.html_safe
    when :satisfaction
      '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
      </svg>'.html_safe
    end
  end
  
  def metric_bg_color(type)
    case type
    when :conversations
      'bg-primary-100 dark:bg-primary-900/30'
    when :resolved
      'bg-green-100 dark:bg-green-900/30'
    when :time
      'bg-yellow-100 dark:bg-yellow-900/30'
    when :satisfaction
      'bg-purple-100 dark:bg-purple-900/30'
    else
      'bg-gray-100 dark:bg-gray-900/30'
    end
  end
  
  def metric_icon_color(type)
    case type
    when :conversations
      'text-primary-600 dark:text-primary-400'
    when :resolved
      'text-green-600 dark:text-green-400'
    when :time
      'text-yellow-600 dark:text-yellow-400'
    when :satisfaction
      'text-purple-600 dark:text-purple-400'
    else
      'text-gray-600 dark:text-gray-400'
    end
  end
end