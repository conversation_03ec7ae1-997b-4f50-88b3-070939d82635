# Rails 8 Advanced Multi-Purpose Chatbot CLI Commands Guide

## Prerequisites
- Ruby 3.2+ installed
- Rails 8.0+ installed
- PostgreSQL 15+ installed
- Node.js 16+ installed
- Docker (optional, for development)

## Phase 1: Project Setup & Basic Configuration

### 1. Create New Rails 8 Application
```bash
# Create new Rails 8 app with PostgreSQL and Tailwind CSS
rails new ai_chatbot_app --database=postgresql --css=tailwind --skip-solid

# Navigate to project directory
cd ai_chatbot_app

# Verify Rails version
rails --version

# Initialize git repository
git init
git add .
git commit -m "Initial Rails 8 application setup"
```

### 2. Database Configuration
```bash
# Create database configuration with multiple databases
# Edit config/database.yml to include primary, cache, and queue databases

# Create databases
rails db:create

# Test database connection
rails db:version
```

### 3. Install Essential Gems
```bash
# Add essential gems to Gemfile
bundle add ruby-openai
bundle add langchainrb
bundle add pgvector
bundle add hotwire-rails
bundle add tailwindcss-rails
bundle add solid_queue
bundle add solid_cache

# Development and testing gems
bundle add rspec-rails --group=development,test
bundle add factory_bot_rails --group=development,test
bundle add faker --group=development,test
bundle add dotenv-rails --group=development,test

# Install gems
bundle install
```

## Phase 2: Rails 8 Solid Stack Setup

### 4. Configure Solid Queue
```bash
# Install Solid Queue
bin/rails solid_queue:install

# Update database.yml to include queue database
# development:
#   primary:
#     <<: *default
#     database: ai_chatbot_app_development
#   queue:
#     <<: *default
#     database: ai_chatbot_app_development_queue
#     migrations_paths: db/queue_migrate

# Run queue migrations
rails db:migrate
```

### 5. Configure Solid Cache
```bash
# Install Solid Cache
bin/rails solid_cache:install

# Update database.yml to include cache database
# development:
#   primary:
#     <<: *default
#     database: ai_chatbot_app_development
#   cache:
#     <<: *default
#     database: ai_chatbot_app_development_cache
#     migrations_paths: db/cache_migrate

# Run cache migrations
rails db:migrate
```

### 6. Setup Hotwire and Turbo
```bash
# Install Hotwire
rails hotwire:install

# Install Turbo
rails turbo:install

# Install Stimulus
rails stimulus:install
```

## Phase 3: Core Models Generation

### 7. Generate User Model with Authentication
```bash
# Generate User model with roles and preferences
rails generate model User \
  email:string \
  role_id:integer \
  preferences:jsonb \
  encrypted_api_key:string \
  locale:string \
  created_at:datetime \
  updated_at:datetime

# Generate Role model
rails generate model Role \
  name:string \
  permissions:jsonb \
  created_at:datetime \
  updated_at:datetime

# Generate Agent model
rails generate model Agent \
  name:string \
  kind:string \
  settings:jsonb \
  description:text \
  status:integer \
  created_at:datetime \
  updated_at:datetime

# Generate AgentAssignment join model
rails generate model AgentAssignment \
  agent_id:integer \
  user_id:integer \
  priority:integer \
  created_at:datetime \
  updated_at:datetime
```

### 8. Generate Conversation and Message Models
```bash
# Generate Conversation model
rails generate model Conversation \
  user_id:integer \
  title:string \
  status:integer \
  metadata:jsonb \
  closed_at:datetime \
  created_at:datetime \
  updated_at:datetime

# Generate Message model
rails generate model Message \
  conversation_id:integer \
  agent_id:integer \
  role:string \
  content:text \
  metadata:jsonb \
  token_count:integer \
  created_at:datetime \
  updated_at:datetime

# Generate Attachment model for file uploads
rails generate model Attachment \
  message_id:integer \
  filename:string \
  content_type:string \
  file_size:integer \
  metadata:jsonb \
  created_at:datetime \
  updated_at:datetime
```

### 9. Generate Knowledge Base Models
```bash
# Generate KnowledgeSource model
rails generate model KnowledgeSource \
  source_type:string \
  uri:string \
  checksum:string \
  content:text \
  metadata:jsonb \
  status:integer \
  created_at:datetime \
  updated_at:datetime

# Generate Embedding model with vector support
rails generate model Embedding \
  knowledge_source_id:integer \
  message_id:integer \
  vector:vector \
  model_name:string \
  metadata:jsonb \
  created_at:datetime \
  updated_at:datetime

# Generate Job model for background processing
rails generate model Job \
  conversation_id:integer \
  worker:string \
  status:integer \
  result:jsonb \
  error_message:text \
  created_at:datetime \
  updated_at:datetime
```

## Phase 4: Database Migrations and Vector Setup

### 10. Setup pgvector Extension
```bash
# Create migration for pgvector extension
rails generate migration EnablePgvectorExtension

# Add to migration file:
# def change
#   enable_extension 'vector'
# end

# Run migrations
rails db:migrate
```

### 11. Create Vector Indexes
```bash
# Create migration for vector indexes
rails generate migration AddVectorIndexes

# Add to migration file:
# def change
#   add_index :embeddings, :vector, using: :hnsw, opclass: :vector_l2_ops
# end

# Run migrations
rails db:migrate
```

### 12. Setup ActiveRecord Encryption
```bash
# Generate encryption keys
bin/rails db:encryption:init

# Add generated keys to Rails credentials
rails credentials:edit

# Create migration for encrypted fields
rails generate migration AddEncryptionToModels

# Run migrations
rails db:migrate
```

## Phase 5: Controllers and Views Generation

### 13. Generate Controllers
```bash
# Generate Application Controller
rails generate controller Application

# Generate Authentication Controller
rails generate controller Authentication \
  login \
  logout \
  register \
  forgot_password

# Generate Dashboard Controller
rails generate controller Dashboard \
  index \
  analytics \
  settings

# Generate Conversations Controller
rails generate controller Conversations \
  index \
  show \
  create \
  update \
  destroy

# Generate Messages Controller
rails generate controller Messages \
  create \
  update \
  destroy \
  stream

# Generate Agents Controller
rails generate controller Agents \
  index \
  show \
  create \
  update \
  destroy

# Generate Admin Controllers
rails generate controller Admin::Base
rails generate controller Admin::Users index show edit update destroy
rails generate controller Admin::Roles index show create edit update destroy
rails generate controller Admin::Agents index show create edit update destroy
rails generate controller Admin::Analytics index reports
```

### 14. Generate API Controllers
```bash
# Generate API base controller
rails generate controller Api::V1::Base

# Generate API authentication controller
rails generate controller Api::V1::Authentication \
  login \
  logout \
  refresh_token

# Generate API conversations controller
rails generate controller Api::V1::Conversations \
  index \
  show \
  create \
  update \
  destroy

# Generate API messages controller
rails generate controller Api::V1::Messages \
  create \
  stream

# Generate API agents controller
rails generate controller Api::V1::Agents \
  index \
  show
```

## Phase 6: Service Objects and Background Jobs

### 15. Generate Service Objects
```bash
# Create services directory
mkdir -p app/services

# Generate conversation service
rails generate service Conversations::Bootstrap
rails generate service Conversations::Manager

# Generate message service
rails generate service Messages::Dispatch
rails generate service Messages::Processor

# Generate agent services
rails generate service Agents::Router
rails generate service Agents::LLM::Chat
rails generate service Agents::LLM::Embedding

# Generate RAG services
rails generate service Rag::Retriever
rails generate service Rag::Assembler
rails generate service Rag::Indexer

# Generate AI services
rails generate service AI::OpenAI::Client
rails generate service AI::OpenAI::ChatCompletion
rails generate service AI::OpenAI::Embedding
```

### 16. Generate Background Jobs
```bash
# Generate AI processing jobs
rails generate job VectorizeMessageJob
rails generate job ConversationSummaryJob
rails generate job AttachmentOcrJob
rails generate job KnowledgeIndexJob
rails generate job AgentTrainingJob

# Generate maintenance jobs
rails generate job VectorPruneJob
rails generate job ConversationCleanupJob
rails generate job LogCleanupJob
rails generate job AnalyticsReportJob
```

## Phase 7: Frontend Assets and Styling

### 17. Setup Tailwind CSS
```bash
# Install Tailwind CSS
rails tailwindcss:install

# Create custom Tailwind configuration
# Edit config/tailwind.config.js

# Create custom CSS files
mkdir -p app/assets/stylesheets/components
mkdir -p app/assets/stylesheets/pages

# Generate component stylesheets
touch app/assets/stylesheets/components/chat.css
touch app/assets/stylesheets/components/agents.css
touch app/assets/stylesheets/components/dashboard.css
touch app/assets/stylesheets/components/forms.css
```

### 18. Generate Stimulus Controllers
```bash
# Generate Stimulus controllers for interactive features
rails generate stimulus chat
rails generate stimulus agent_selector
rails generate stimulus message_form
rails generate stimulus typing_indicator
rails generate stimulus file_upload
rails generate stimulus search
rails generate stimulus voice_input
```

## Phase 8: Configuration and Initializers

### 19. Generate Configuration Files
```bash
# Create initializers
touch config/initializers/openai.rb
touch config/initializers/langchain.rb
touch config/initializers/solid_queue.rb
touch config/initializers/solid_cache.rb
touch config/initializers/cors.rb
touch config/initializers/session_store.rb

# Create configuration files
touch config/agents.yml
touch config/knowledge_sources.yml
touch config/ai_models.yml
```

### 20. Generate Localization Files
```bash
# Generate locale files
rails generate locale en
rails generate locale es
rails generate locale fr

# Create specific locale files
touch config/locales/agents.en.yml
touch config/locales/conversations.en.yml
touch config/locales/errors.en.yml
```

## Phase 9: Testing Setup

### 21. Generate Test Files
```bash
# Initialize RSpec
rails generate rspec:install

# Generate model specs
rails generate rspec:model User
rails generate rspec:model Conversation
rails generate rspec:model Message
rails generate rspec:model Agent
rails generate rspec:model KnowledgeSource
rails generate rspec:model Embedding

# Generate controller specs
rails generate rspec:controller Conversations
rails generate rspec:controller Messages
rails generate rspec:controller Agents
rails generate rspec:controller Authentication

# Generate service specs
rails generate rspec:service Conversations::Bootstrap
rails generate rspec:service Messages::Dispatch
rails generate rspec:service Agents::Router

# Generate system specs
rails generate rspec:system ConversationFlow
rails generate rspec:system AgentInteraction
rails generate rspec:system Authentication
```

### 22. Generate Factories
```bash
# Generate factory files
rails generate factory_bot:model User
rails generate factory_bot:model Conversation
rails generate factory_bot:model Message
rails generate factory_bot:model Agent
rails generate factory_bot:model KnowledgeSource
rails generate factory_bot:model Embedding
```

## Phase 10: Security and Performance

### 23. Generate Security Features
```bash
# Generate security-related files
rails generate migration AddSecurityColumns
rails generate controller Security::Base
rails generate job SecurityAuditJob

# Generate rate limiting
rails generate migration CreateRateLimits
rails generate model RateLimit
```

### 24. Generate Performance Monitoring
```bash
# Generate analytics models
rails generate model Analytics::Event \
  event_type:string \
  user_id:integer \
  conversation_id:integer \
  metadata:jsonb \
  created_at:datetime

rails generate model Analytics::Usage \
  user_id:integer \
  tokens_used:integer \
  cost:decimal \
  period:date \
  created_at:datetime

# Generate performance jobs
rails generate job Analytics::ProcessEventJob
rails generate job Analytics::GenerateReportJob
```

## Phase 11: Development Tools

### 25. Generate Development Helpers
```bash
# Generate seed files
touch db/seeds.rb
touch db/seeds/development.rb
touch db/seeds/production.rb

# Generate rake tasks
rails generate task ai:setup
rails generate task ai:seed_knowledge
rails generate task ai:train_agents
rails generate task ai:generate_embeddings

# Generate generators
rails generate generator agent
rails generate generator knowledge_source
rails generate generator conversation_template
```

### 26. Generate Docker Configuration
```bash
# Generate Docker files
touch Dockerfile
touch docker-compose.yml
touch docker-compose.override.yml
touch .dockerignore

# Generate deployment files
mkdir -p config/deploy
touch config/deploy/production.rb
touch config/deploy/staging.rb
```

## Phase 12: Finalization and Setup

### 27. Run All Migrations
```bash
# Run all pending migrations
rails db:migrate

# Run migrations for all databases
rails db:migrate:primary
rails db:migrate:cache
rails db:migrate:queue

# Generate schema files
rails db:schema:dump
```

### 28. Setup Initial Data
```bash
# Run seeds
rails db:seed

# Setup encryption keys
rails credentials:edit

# Setup environment variables
cp .env.example .env
```

### 29. Configure Development Environment
```bash
# Setup development processes
echo "web: bin/rails server" > Procfile.dev
echo "css: bin/rails tailwindcss:watch" >> Procfile.dev
echo "js: bin/rails assets:watch" >> Procfile.dev
echo "queue: bin/jobs" >> Procfile.dev

# Make dev script executable
chmod +x bin/dev
```

### 30. Final Validation
```bash
# Test application startup
bin/dev

# Run test suite
bundle exec rspec

# Check security
bundle exec brakeman

# Check code quality
bundle exec rubocop

# Final commit
git add .
git commit -m "Complete Rails 8 Advanced Multi-Purpose Chatbot setup"
```

## Environment Variables Setup

Create `.env` file with the following:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORGANIZATION_ID=your_org_id_here

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/ai_chatbot_app_development
QUEUE_DATABASE_URL=postgresql://username:password@localhost/ai_chatbot_app_development_queue
CACHE_DATABASE_URL=postgresql://username:password@localhost/ai_chatbot_app_development_cache

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0

# Application Configuration
SECRET_KEY_BASE=your_secret_key_base_here
RAILS_MASTER_KEY=your_rails_master_key_here

# AI Configuration
DEFAULT_AI_MODEL=gpt-4o
EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=2048
TEMPERATURE=0.7

# Performance Configuration
MAX_CONVERSATIONS_PER_USER=10
MAX_MESSAGES_PER_CONVERSATION=100
VECTOR_DIMENSIONS=1536
```

## Next Steps

1. **Configure AI Models**: Set up OpenAI API keys and configure model settings
2. **Implement Business Logic**: Add the specific business logic for each service object
3. **Create Knowledge Base**: Import initial knowledge sources and generate embeddings
4. **Setup Monitoring**: Configure application monitoring and logging
5. **Deploy**: Set up production deployment pipeline
6. **Test**: Run comprehensive tests and user acceptance testing

## Key Features Implemented

- **Multi-Agent Architecture**: Specialized agents for different domains
- **Vector Search**: pgvector integration for semantic search
- **Real-time Chat**: Hotwire/Turbo for real-time interactions
- **Background Processing**: Solid Queue for async tasks
- **Caching**: Solid Cache for performance optimization
- **Security**: ActiveRecord encryption and secure authentication
- **Scalability**: Modular architecture with service objects
- **Modern UI**: Tailwind CSS with responsive design
- **Testing**: Comprehensive test suite with RSpec
- **Monitoring**: Analytics and performance tracking

This setup provides a solid foundation for building an advanced multi-purpose chatbot application with Rails 8.