# AI Integration Guide

This platform supports multiple AI providers through the `ruby_llm` gem, providing a unified interface for various Large Language Models (LLMs).

## Supported Providers

### 1. OpenAI
- Models: GPT-4o, GPT-4o-mini, GPT-4 Turbo, GPT-3.5 Turbo
- Features: Chat completion, embeddings, function calling
- Configuration: `OPENAI_API_KEY`

### 2. <PERSON><PERSON><PERSON> (<PERSON>)
- Models: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- Features: Chat completion, advanced reasoning
- Configuration: `ANTHROPIC_API_KEY`

### 3. Google Gemini
- Models: Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini 1.0 Pro
- Features: Chat completion, multimodal capabilities
- Status: Pending implementation

### 4. DeepSeek
- Models: DeepSeek Chat, DeepSeek Coder
- Features: Chat completion, code generation
- Configuration: `DEEPSEEK_API_KEY`
- Note: Uses OpenAI-compatible API

### 5. <PERSON><PERSON> (Moonshot AI)
- Models: Moonshot v1
- Features: Long context window (up to 200k tokens)
- Status: Can be integrated via OpenRouter

### 6. OpenRouter
- Access to multiple models from various providers
- Models: All major LLMs including Llama, Mixtral, etc.
- Configuration: `OPENROUTER_API_KEY`

### 7. <PERSON><PERSON>ma (Local Models)
- Models: Llama 3.1, Mistral, Phi 3, Qwen 2.5
- Features: Privacy-focused, runs locally
- Configuration: `OLLAMA_BASE_URL` (default: http://localhost:11434)

### 8. AWS Bedrock
- Models: Claude (via Bedrock), Titan, Llama
- Features: Enterprise-grade, AWS integration
- Status: Pending implementation

## Configuration

### Environment Variables

```bash
# Required for OpenAI models
export OPENAI_API_KEY="sk-..."

# Required for Anthropic models
export ANTHROPIC_API_KEY="sk-ant-..."

# For DeepSeek
export DEEPSEEK_API_KEY="sk-..."

# For OpenRouter (access to multiple models)
export OPENROUTER_API_KEY="sk-or-..."

# For local Ollama instance
export OLLAMA_BASE_URL="http://localhost:11434"
```

### Rails Credentials

You can also configure API keys using Rails credentials:

```yaml
# config/credentials.yml.enc
openai:
  api_key: sk-...

anthropic:
  api_key: sk-ant-...

deepseek:
  api_key: sk-...
```

## Usage

### Basic Chat Completion

```ruby
# Create an agent with specific AI provider
agent = Agent.create!(
  tenant: tenant,
  name: "AI Assistant",
  kind: "customer_service",
  llm_provider: "openai",
  llm_model: "gpt-4o"
)

# Use the agent to generate responses
conversation = Conversation.create!(tenant: tenant, agent: agent)
chat_service = ChatCompletionService.new(conversation: conversation)

# Process a message
response = chat_service.process_message(
  content: "How can I track my order?"
)

puts response.content
```

### Streaming Responses

```ruby
chat_service.process_message_stream(content: "Tell me a story") do |chunk, message|
  print chunk # Real-time streaming output
end
```

### Different Providers for Different Agents

```ruby
# Customer service agent using OpenAI
customer_agent = Agent.create!(
  llm_provider: "openai",
  llm_model: "gpt-4o-mini",
  kind: "customer_service"
)

# Technical support using Claude
tech_agent = Agent.create!(
  llm_provider: "anthropic",
  llm_model: "claude-3-sonnet-20240229",
  kind: "technical_support"
)

# Sales agent using DeepSeek
sales_agent = Agent.create!(
  llm_provider: "deepseek",
  llm_model: "deepseek-chat",
  kind: "sales"
)
```

### Provider-Specific Settings

Each agent can have custom settings:

```ruby
agent.update!(
  settings: {
    "temperature" => 0.7,
    "max_tokens" => 1000,
    "custom_instructions" => "Always be helpful and concise",
    "api_key" => "agent-specific-key" # Optional: Override global API key
  }
)
```

## Testing

Run the AI demo to test your configuration:

```bash
# Test all configured providers
rails ai:demo

# List available models
rails ai:models
```

## Best Practices

1. **API Key Security**: Never commit API keys. Use environment variables or Rails credentials.

2. **Provider Selection**: Choose providers based on your needs:
   - OpenAI: General purpose, wide model selection
   - Anthropic: Advanced reasoning, safety-focused
   - DeepSeek: Cost-effective, good for coding
   - Ollama: Privacy-focused, local deployment

3. **Error Handling**: The system gracefully handles API errors and rate limits.

4. **Token Management**: Monitor token usage through message metadata.

5. **Multi-tenancy**: Each tenant can have different default providers and settings.

## Extending Support

To add a new AI provider:

1. Update the `AiService` class with a new provider method
2. Add the provider to the Agent model validation
3. Configure API credentials
4. Update the available models list

Example:
```ruby
# In app/services/ai_service.rb
def chat_with_new_provider(messages, **options)
  # Implementation
end
```