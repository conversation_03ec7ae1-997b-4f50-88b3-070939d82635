namespace :ai do
  desc "Demonstrate AI service integration with multiple providers"
  task demo: :environment do
    puts "\n🤖 AI Service Demo - Multi-Provider Support\n"
    puts "=" * 50
    
    # Create a demo tenant and agent
    tenant = Tenant.first_or_create!(
      name: "Demo Company",
      subdomain: "demo",
      settings: {
        "default_llm_provider" => "openai",
        "default_llm_model" => "gpt-4o-mini"
      }
    )
    
    # Demo different agent types with different AI providers
    agents = [
      {
        name: "Alice (Customer Service)",
        kind: "customer_service",
        llm_provider: "openai",
        llm_model: "gpt-4o-mini"
      },
      {
        name: "<PERSON> (Sales Expert)",
        kind: "sales",
        llm_provider: "anthropic",
        llm_model: "claude-3-haiku-20240307"
      },
      {
        name: "<PERSON> (Tech Support)",
        kind: "technical_support",
        llm_provider: "openai",
        llm_model: "gpt-3.5-turbo"
      }
    ]
    
    agents.each do |agent_attrs|
      agent = Agent.find_or_create_by!(
        tenant: tenant,
        name: agent_attrs[:name],
        kind: agent_attrs[:kind]
      ) do |a|
        a.llm_provider = agent_attrs[:llm_provider]
        a.llm_model = agent_attrs[:llm_model]
      end
      
      puts "\n📌 Agent: #{agent.name}"
      puts "   Type: #{agent.kind.humanize}"
      puts "   Provider: #{agent.llm_provider}"
      puts "   Model: #{agent.llm_model}"
      
      # Skip demo if API key not configured
      api_key_env = "#{agent.llm_provider.upcase}_API_KEY"
      unless ENV[api_key_env]
        puts "   ⚠️  Skipping demo - #{api_key_env} not set"
        next
      end
      
      # Create a conversation
      conversation = Conversation.create!(
        tenant: tenant,
        agent: agent,
        metadata: { demo: true }
      )
      
      # Test the chat service
      chat_service = ChatCompletionService.new(conversation: conversation)
      
      begin
        # User message
        user_message = case agent.kind
        when "customer_service"
          "Hello! I ordered a product last week but haven't received it yet. Can you help?"
        when "sales"
          "I'm looking for a laptop for programming. What do you recommend?"
        when "technical_support"
          "My application is showing a 500 error. How can I debug this?"
        else
          "Hello! How can you help me today?"
        end
        
        puts "\n   💬 User: #{user_message}"
        
        # Get AI response
        response = chat_service.process_message(content: user_message)
        
        puts "   🤖 #{agent.name}: #{response.content[0..200]}#{'...' if response.content.length > 200}"
        puts "   📊 Tokens used: #{response.metadata.dig('usage', 'total_tokens') || 'N/A'}"
        
      rescue => e
        puts "   ❌ Error: #{e.message}"
      end
    end
    
    puts "\n" + "=" * 50
    puts "✅ Demo completed!"
    puts "\nTo test with different providers:"
    puts "1. Set the appropriate API keys in your environment:"
    puts "   - OPENAI_API_KEY"
    puts "   - ANTHROPIC_API_KEY"
    puts "   - DEEPSEEK_API_KEY (uses OpenAI-compatible API)"
    puts "   - OPENROUTER_API_KEY (access to multiple models)"
    puts "\n2. Run: rails ai:demo"
  end
  
  desc "List available AI models for each provider"
  task models: :environment do
    puts "\n📋 Available AI Models by Provider\n"
    puts "=" * 50
    
    tenant = Tenant.first_or_create!(name: "Demo", subdomain: "demo")
    
    %w[openai anthropic google deepseek openrouter ollama bedrock].each do |provider|
      agent = Agent.new(tenant: tenant, llm_provider: provider)
      service = AiService.new(agent: agent)
      
      puts "\n#{provider.upcase}:"
      begin
        models = service.available_models
        models.each { |model| puts "  - #{model}" }
      rescue => e
        puts "  ❌ Error: #{e.message}"
      end
    end
  end
end