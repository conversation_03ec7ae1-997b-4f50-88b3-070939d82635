class CreateCustomers < ActiveRecord::Migration[8.0]
  def change
    create_table :customers do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :integration, null: false, foreign_key: true
      t.string :external_id
      t.string :email
      t.string :first_name
      t.string :last_name
      t.string :phone
      t.decimal :total_spent
      t.integer :orders_count
      t.jsonb :data

      t.timestamps
    end
  end
end
