class CreateIntegrations < ActiveRecord::Migration[8.0]
  def change
    create_table :integrations do |t|
      t.references :tenant, null: false, foreign_key: true
      t.string :platform
      t.text :api_key
      t.text :api_secret
      t.string :shop_domain
      t.string :webhook_url
      t.integer :status
      t.datetime :last_sync_at
      t.jsonb :settings

      t.timestamps
    end
  end
end
