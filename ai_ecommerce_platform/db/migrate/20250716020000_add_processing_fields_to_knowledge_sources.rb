class AddProcessingFieldsToKnowledgeSources < ActiveRecord::Migration[8.0]
  def change
    add_column :knowledge_sources, :processed_at, :datetime unless column_exists?(:knowledge_sources, :processed_at)
    add_column :knowledge_sources, :chunk_count, :integer, default: 0 unless column_exists?(:knowledge_sources, :chunk_count)
    add_column :knowledge_sources, :error_message, :text unless column_exists?(:knowledge_sources, :error_message)
    
    add_index :knowledge_sources, :status unless index_exists?(:knowledge_sources, :status)
  end
end