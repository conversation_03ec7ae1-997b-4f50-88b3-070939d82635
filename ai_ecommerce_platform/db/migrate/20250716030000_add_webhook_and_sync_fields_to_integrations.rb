class AddWebhookAndSyncFieldsToIntegrations < ActiveRecord::Migration[8.0]
  def change
    add_column :integrations, :webhook_token, :string unless column_exists?(:integrations, :webhook_token)
    add_column :integrations, :sync_status, :string unless column_exists?(:integrations, :sync_status)
    add_column :integrations, :sync_results, :jsonb unless column_exists?(:integrations, :sync_results)
    add_column :integrations, :sync_error, :text unless column_exists?(:integrations, :sync_error)
    
    add_index :integrations, :webhook_token, unique: true unless index_exists?(:integrations, :webhook_token)
    add_index :integrations, :sync_status unless index_exists?(:integrations, :sync_status)
  end
end