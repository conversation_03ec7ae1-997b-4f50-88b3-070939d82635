class CreateAgentHandoffs < ActiveRecord::Migration[8.0]
  def change
    create_table :agent_handoffs do |t|
      t.references :conversation, null: false, foreign_key: true
      t.references :from_agent, null: false, foreign_key: { to_table: :agents }
      t.references :to_agent, null: false, foreign_key: { to_table: :agents }
      t.text :reason
      t.jsonb :metadata

      t.timestamps
    end
  end
end
