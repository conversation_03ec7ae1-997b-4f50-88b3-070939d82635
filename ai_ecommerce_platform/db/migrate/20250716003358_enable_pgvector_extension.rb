class EnablePgvectorExtension < ActiveRecord::Migration[8.0]
  def up
    # Note: pgvector extension must be installed on your PostgreSQL server
    # For macOS: brew install pgvector
    # For Ubuntu: sudo apt install postgresql-15-pgvector
    # For other systems, see: https://github.com/pgvector/pgvector
    
    # Check if extension is available before trying to enable it
    extension_available = ActiveRecord::Base.connection.select_value(
      "SELECT 1 FROM pg_available_extensions WHERE name = 'vector'"
    )
    
    if extension_available
      enable_extension 'vector'
    else
      say "pgvector extension not available. Vector search features will be disabled.", true
      say "To enable vector search, install pgvector and run migrations again.", true
    end
  end
  
  def down
    disable_extension 'vector' if extension_enabled?('vector')
  end
end
