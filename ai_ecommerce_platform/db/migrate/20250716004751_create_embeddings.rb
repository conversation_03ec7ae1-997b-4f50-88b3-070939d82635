class CreateEmbeddings < ActiveRecord::Migration[8.0]
  def change
    create_table :embeddings do |t|
      t.string :embeddable_type
      t.integer :embeddable_id
      
      # Check if vector type is available (pgvector installed)
      if connection.data_source_exists?('pg_available_extensions') && 
         connection.select_value("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
        t.vector :vector, limit: 1536 # OpenAI embeddings dimension
      else
        # Fallback to array of floats if pgvector not available
        t.float :vector, array: true
        say "Using float array for embeddings. Install pgvector for better performance.", true
      end
      
      t.string :embedding_model
      t.jsonb :metadata

      t.timestamps
    end
    
    add_index :embeddings, [:embeddable_type, :embeddable_id]
  end
end
