class CreateProducts < ActiveRecord::Migration[8.0]
  def change
    create_table :products do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :integration, null: false, foreign_key: true
      t.string :external_id
      t.string :name
      t.text :description
      t.decimal :price
      t.string :sku
      t.integer :inventory_quantity
      t.string :status
      t.jsonb :data

      t.timestamps
    end
  end
end
