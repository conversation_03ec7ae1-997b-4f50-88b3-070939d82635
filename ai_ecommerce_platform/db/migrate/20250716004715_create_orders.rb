class CreateOrders < ActiveRecord::Migration[8.0]
  def change
    create_table :orders do |t|
      t.references :tenant, null: false, foreign_key: true
      t.references :integration, null: false, foreign_key: true
      t.string :external_id
      t.string :order_number
      t.string :customer_email
      t.string :status
      t.decimal :total_amount
      t.string :currency
      t.jsonb :data
      t.datetime :ordered_at

      t.timestamps
    end
  end
end
