# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_16_113000) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "agent_handoffs", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.bigint "from_agent_id", null: false
    t.bigint "to_agent_id", null: false
    t.text "reason"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_agent_handoffs_on_conversation_id"
    t.index ["from_agent_id"], name: "index_agent_handoffs_on_from_agent_id"
    t.index ["to_agent_id"], name: "index_agent_handoffs_on_to_agent_id"
  end

  create_table "agent_specializations", force: :cascade do |t|
    t.bigint "agent_id", null: false
    t.string "domain"
    t.integer "expertise_level"
    t.jsonb "configuration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agent_id"], name: "index_agent_specializations_on_agent_id"
  end

  create_table "agents", force: :cascade do |t|
    t.string "name"
    t.string "kind"
    t.jsonb "capabilities"
    t.jsonb "settings"
    t.text "description"
    t.integer "status"
    t.bigint "tenant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_agents_on_tenant_id"
  end

  create_table "conversation_insights", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.float "sentiment"
    t.string "intent"
    t.jsonb "entities"
    t.jsonb "key_topics"
    t.text "summary"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_conversation_insights_on_conversation_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "customer_email"
    t.string "customer_name"
    t.string "channel"
    t.integer "status"
    t.jsonb "metadata"
    t.datetime "started_at"
    t.datetime "ended_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "assigned_agent_id"
    t.index ["assigned_agent_id"], name: "index_conversations_on_assigned_agent_id"
    t.index ["tenant_id"], name: "index_conversations_on_tenant_id"
  end

  create_table "customers", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "integration_id", null: false
    t.string "external_id"
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.string "phone"
    t.decimal "total_spent"
    t.integer "orders_count"
    t.jsonb "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_customers_on_integration_id"
    t.index ["tenant_id"], name: "index_customers_on_tenant_id"
  end

  create_table "embeddings", force: :cascade do |t|
    t.string "embeddable_type"
    t.integer "embeddable_id"
    t.float "vector", array: true
    t.string "embedding_model"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["embeddable_type", "embeddable_id"], name: "index_embeddings_on_embeddable_type_and_embeddable_id"
  end

  create_table "integrations", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "platform"
    t.text "api_key"
    t.text "api_secret"
    t.string "shop_domain"
    t.string "webhook_url"
    t.integer "status"
    t.datetime "last_sync_at"
    t.jsonb "settings"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "webhook_token"
    t.string "sync_status"
    t.jsonb "sync_results"
    t.text "sync_error"
    t.index ["sync_status"], name: "index_integrations_on_sync_status"
    t.index ["tenant_id"], name: "index_integrations_on_tenant_id"
    t.index ["webhook_token"], name: "index_integrations_on_webhook_token", unique: true
  end

  create_table "knowledge_sources", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.string "source_type"
    t.string "title"
    t.text "content"
    t.string "uri"
    t.string "checksum"
    t.integer "status"
    t.jsonb "metadata"
    t.datetime "indexed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "processed_at"
    t.integer "chunk_count", default: 0
    t.text "error_message"
    t.index ["status"], name: "index_knowledge_sources_on_status"
    t.index ["tenant_id"], name: "index_knowledge_sources_on_tenant_id"
  end

  create_table "messages", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.string "sender_type"
    t.integer "sender_id"
    t.text "content"
    t.jsonb "metadata"
    t.integer "tokens_used"
    t.boolean "embedding_generated"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_messages_on_conversation_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "integration_id", null: false
    t.string "external_id"
    t.string "order_number"
    t.string "customer_email"
    t.string "status"
    t.decimal "total_amount"
    t.string "currency"
    t.jsonb "data"
    t.datetime "ordered_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_orders_on_integration_id"
    t.index ["tenant_id"], name: "index_orders_on_tenant_id"
  end

  create_table "products", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "integration_id", null: false
    t.string "external_id"
    t.string "name"
    t.text "description"
    t.decimal "price"
    t.string "sku"
    t.integer "inventory_quantity"
    t.string "status"
    t.jsonb "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_products_on_integration_id"
    t.index ["tenant_id"], name: "index_products_on_tenant_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.jsonb "permissions"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tenant_users", force: :cascade do |t|
    t.bigint "tenant_id", null: false
    t.bigint "user_id", null: false
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tenant_id"], name: "index_tenant_users_on_tenant_id"
    t.index ["user_id"], name: "index_tenant_users_on_user_id"
  end

  create_table "tenants", force: :cascade do |t|
    t.string "name"
    t.string "subdomain"
    t.string "custom_domain"
    t.jsonb "settings"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "api_key"
    t.jsonb "branding_config", default: {}
    t.jsonb "widget_config", default: {}
    t.boolean "active", default: true
    t.index ["active"], name: "index_tenants_on_active"
    t.index ["api_key"], name: "index_tenants_on_api_key", unique: true
    t.index ["subdomain"], name: "index_tenants_on_subdomain", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "first_name"
    t.string "last_name"
    t.integer "role"
    t.jsonb "preferences"
    t.string "locale"
    t.bigint "tenant_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false, null: false
    t.bigint "current_tenant_id"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.index ["admin"], name: "index_users_on_admin"
    t.index ["current_tenant_id"], name: "index_users_on_current_tenant_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["tenant_id"], name: "index_users_on_tenant_id"
  end

  add_foreign_key "agent_handoffs", "agents", column: "from_agent_id"
  add_foreign_key "agent_handoffs", "agents", column: "to_agent_id"
  add_foreign_key "agent_handoffs", "conversations"
  add_foreign_key "agent_specializations", "agents"
  add_foreign_key "agents", "tenants"
  add_foreign_key "conversation_insights", "conversations"
  add_foreign_key "conversations", "agents", column: "assigned_agent_id"
  add_foreign_key "conversations", "tenants"
  add_foreign_key "customers", "integrations"
  add_foreign_key "customers", "tenants"
  add_foreign_key "integrations", "tenants"
  add_foreign_key "knowledge_sources", "tenants"
  add_foreign_key "messages", "conversations"
  add_foreign_key "orders", "integrations"
  add_foreign_key "orders", "tenants"
  add_foreign_key "products", "integrations"
  add_foreign_key "products", "tenants"
  add_foreign_key "tenant_users", "tenants"
  add_foreign_key "tenant_users", "users"
  add_foreign_key "users", "tenants", column: "current_tenant_id"
end
