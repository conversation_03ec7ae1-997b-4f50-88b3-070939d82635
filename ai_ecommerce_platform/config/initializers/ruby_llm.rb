RubyLLM.configure do |config|
  # OpenAI Configuration
  config.openai_api_key = Rails.application.credentials.dig(:openai, :api_key) || ENV.fetch('OPENAI_API_KEY', nil)
  
  # Anthropic Configuration  
  config.anthropic_api_key = Rails.application.credentials.dig(:anthropic, :api_key) || ENV.fetch('ANTHROPIC_API_KEY', nil)
  
  # Additional provider keys can be configured as needed
  # The ruby_llm gem will handle provider-specific configurations internally
end