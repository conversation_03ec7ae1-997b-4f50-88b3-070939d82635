source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"
# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem "propshaft"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing", "~> 1.2"

# Multi-tenancy support
gem "acts_as_tenant"

# Analytics and charting
gem "groupdate"
gem "chartkick"

# Authentication & Authorization
gem "devise"
gem "devise-jwt"
gem "pundit"

# Rails 8 uses Solid Cache and Solid Cable instead of Redis

# E-commerce Platform Integrations
gem "shopify_app"
gem "woocommerce_api"
# Note: bigcommerce gem has compatibility issues with newer faraday versions
# We'll implement a custom integration using faraday directly

# AI & Machine Learning
gem "ruby_llm" # Unified interface for multiple LLM providers
gem "ruby-openai"
gem "anthropic"
gem "langchainrb"
gem "neighbor" # for pgvector support
gem "tiktoken_ruby" # for token counting

# Background Processing - Using Rails 8 Solid Queue instead of Sidekiq
# Solid Queue is already included above

# API Development
gem "rack-cors"
gem "jwt"
gem "oj" # Fast JSON parser

# File Processing
gem "pdf-reader"
gem "roo" # For Excel/CSV processing
gem "mini_magick"

# Monitoring & Performance
gem "sentry-rails"
gem "sentry-ruby"
gem "newrelic_rpm"

# HTTP Clients
gem "faraday"
gem "httparty"

# Utilities
gem "friendly_id"
gem "kaminari" # Pagination
gem "ransack" # Advanced search
gem "aasm" # State machines
gem "paper_trail" # Auditing
gem "discard" # Soft deletes

# Internationalization
gem "http_accept_language"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false
  
  # Testing
  gem "rspec-rails"
  gem "factory_bot_rails"
  gem "faker"
  gem "shoulda-matchers"
  
  # Environment variables
  gem "dotenv-rails"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver"
end
