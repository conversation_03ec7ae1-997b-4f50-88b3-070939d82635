require 'rails_helper'

RSpec.describe Ecommerce::ShopifyIntegration do
  let(:tenant) { create(:tenant) }
  let(:integration) { create(:integration, tenant: tenant, platform: 'shopify') }
  let(:service) { described_class.new(integration) }

  describe '#sync_products' do
    let(:shopify_products_response) do
      {
        'products' => [
          {
            'id' => 12345,
            'title' => 'Test Product',
            'body_html' => '<p>Product description</p>',
            'variants' => [
              {
                'price' => '29.99',
                'sku' => 'TEST-001',
                'inventory_quantity' => 100
              }
            ]
          }
        ]
      }
    end

    before do
      allow_any_instance_of(ShopifyAPI::Client).to receive(:get)
        .with('products')
        .and_return(shopify_products_response)
    end

    it 'creates new products' do
      expect { service.sync_products }.to change { Product.count }.by(1)
    end

    it 'updates existing products' do
      product = create(:product, integration: integration, external_id: '12345')
      
      service.sync_products
      
      product.reload
      expect(product.name).to eq('Test Product')
      expect(product.price).to eq(29.99)
    end

    it 'returns success with synced count' do
      result = service.sync_products
      
      expect(result[:success]).to be true
      expect(result[:synced_count]).to eq(1)
    end
  end

  describe '#test_connection' do
    context 'when connection is successful' do
      before do
        allow_any_instance_of(ShopifyAPI::Client).to receive(:get)
          .with('shop')
          .and_return({ 'shop' => { 'name' => 'Test Shop' } })
      end

      it 'returns success' do
        result = service.test_connection
        
        expect(result[:success]).to be true
        expect(result[:message]).to eq('Connection successful')
      end
    end

    context 'when connection fails' do
      before do
        allow_any_instance_of(ShopifyAPI::Client).to receive(:get)
          .with('shop')
          .and_raise(StandardError.new('Connection failed'))
      end

      it 'returns error' do
        result = service.test_connection
        
        expect(result[:success]).to be false
        expect(result[:error]).to eq('Connection failed')
      end
    end
  end
end