require 'rails_helper'

RSpec.describe KnowledgeBase::VectorSearch do
  let(:tenant) { create(:tenant) }
  let(:search_service) { described_class.new(tenant) }
  
  let(:knowledge_source) { create(:knowledge_source, tenant: tenant, name: 'Test Document') }
  let!(:embedding1) { create(:embedding, knowledge_source: knowledge_source, content: 'How to reset password', embedding: Array.new(1536, 0.1)) }
  let!(:embedding2) { create(:embedding, knowledge_source: knowledge_source, content: 'Product return policy', embedding: Array.new(1536, 0.2)) }
  let!(:embedding3) { create(:embedding, knowledge_source: knowledge_source, content: 'Shipping information', embedding: Array.new(1536, 0.3)) }

  before do
    allow_any_instance_of(OpenAI::Client).to receive(:embeddings).and_return({
      'data' => [{ 'embedding' => Array.new(1536, 0.15) }]
    })
  end

  describe '#search' do
    it 'returns relevant results' do
      results = search_service.search('password reset', limit: 2)
      
      expect(results.length).to eq(2)
      expect(results.first[:content]).to include('password')
    end

    it 'includes similarity scores' do
      results = search_service.search('password reset')
      
      results.each do |result|
        expect(result[:similarity_score]).to be_between(0, 1)
      end
    end

    it 'respects threshold parameter' do
      results = search_service.search('completely unrelated query', threshold: 0.9)
      
      expect(results).to be_empty
    end
  end

  describe '#semantic_search' do
    let(:knowledge_source2) { create(:knowledge_source, tenant: tenant, source_type: 'url') }
    let!(:embedding4) { create(:embedding, knowledge_source: knowledge_source2, content: 'URL content') }

    it 'filters by source type' do
      results = search_service.semantic_search(
        'test query',
        filters: { source_types: ['url'] }
      )
      
      expect(results.all? { |r| r[:source_type] == 'url' }).to be true
    end

    it 'filters by creation date' do
      old_source = create(:knowledge_source, tenant: tenant, created_at: 2.months.ago)
      create(:embedding, knowledge_source: old_source)
      
      results = search_service.semantic_search(
        'test query',
        filters: { created_after: 1.month.ago }
      )
      
      expect(results).not_to include(have_attributes(knowledge_source: old_source))
    end
  end

  describe '#find_similar' do
    it 'finds similar embeddings' do
      results = search_service.find_similar(embedding1.id, limit: 2)
      
      expect(results.length).to eq(2)
      expect(results.map { |r| r[:id] }).not_to include(embedding1.id)
    end

    it 'orders by similarity' do
      results = search_service.find_similar(embedding1.id)
      
      similarity_scores = results.map { |r| r[:similarity_score] }
      expect(similarity_scores).to eq(similarity_scores.sort.reverse)
    end
  end
end