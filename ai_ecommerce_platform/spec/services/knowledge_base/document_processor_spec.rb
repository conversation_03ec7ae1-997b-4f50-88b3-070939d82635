require 'rails_helper'

RSpec.describe KnowledgeBase::DocumentProcessor do
  let(:tenant) { create(:tenant) }
  let(:knowledge_source) { create(:knowledge_source, tenant: tenant, source_type: 'text', content: 'This is a test document with multiple sentences. It should be processed and chunked properly.') }
  let(:processor) { described_class.new(knowledge_source) }

  describe '#process' do
    before do
      allow_any_instance_of(OpenAI::Client).to receive(:embeddings).and_return({
        'data' => [{ 'embedding' => Array.new(1536, 0.1) }]
      })
    end

    it 'creates embeddings for the document' do
      expect { processor.process }.to change { Embedding.count }
    end

    it 'updates knowledge source status to processed' do
      processor.process
      
      knowledge_source.reload
      expect(knowledge_source.status).to eq('processed')
      expect(knowledge_source.processed_at).to be_present
    end

    it 'returns success with chunk count' do
      result = processor.process
      
      expect(result[:success]).to be true
      expect(result[:chunks_created]).to be > 0
    end

    context 'when processing fails' do
      before do
        allow_any_instance_of(OpenAI::Client).to receive(:embeddings)
          .and_raise(StandardError.new('API error'))
      end

      it 'updates status to failed' do
        processor.process
        
        knowledge_source.reload
        expect(knowledge_source.status).to eq('failed')
        expect(knowledge_source.error_message).to eq('API error')
      end

      it 'returns error' do
        result = processor.process
        
        expect(result[:success]).to be false
        expect(result[:error]).to eq('API error')
      end
    end
  end

  describe 'chunking' do
    let(:long_content) { 'word ' * 2000 } # 2000 words
    let(:knowledge_source) { create(:knowledge_source, tenant: tenant, source_type: 'text', content: long_content) }

    before do
      allow_any_instance_of(OpenAI::Client).to receive(:embeddings).and_return({
        'data' => [{ 'embedding' => Array.new(1536, 0.1) }]
      })
    end

    it 'creates multiple chunks for long documents' do
      processor.process
      
      expect(knowledge_source.embeddings.count).to be > 1
    end

    it 'maintains chunk overlap' do
      processor.process
      
      chunks = knowledge_source.embeddings.order(:chunk_index).pluck(:content)
      
      # Check that consecutive chunks have some overlap
      chunks.each_cons(2) do |chunk1, chunk2|
        chunk1_words = chunk1.split.last(KnowledgeBase::DocumentProcessor::CHUNK_OVERLAP)
        chunk2_words = chunk2.split.first(KnowledgeBase::DocumentProcessor::CHUNK_OVERLAP)
        
        expect(chunk1_words).to have_some_overlap_with(chunk2_words)
      end
    end
  end
end

RSpec::Matchers.define :have_some_overlap_with do |expected|
  match do |actual|
    (actual & expected).any?
  end
end