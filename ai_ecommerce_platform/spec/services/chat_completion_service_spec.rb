require 'rails_helper'

RSpec.describe ChatCompletionService do
  let(:tenant) { create(:tenant) }
  let(:agent) { create(:agent, tenant: tenant, kind: 'customer_service') }
  let(:conversation) { create(:conversation, tenant: tenant, agent: agent) }
  let(:service) { described_class.new(conversation: conversation) }

  describe '#process_message' do
    let(:user_content) { 'I need help with my order' }
    let(:ai_response) do
      {
        content: "I'd be happy to help you with your order. Could you please provide your order number?",
        role: 'assistant',
        finish_reason: 'stop',
        usage: { total_tokens: 50 }
      }
    end

    before do
      allow_any_instance_of(Agent).to receive(:generate_response).and_return(ai_response)
    end

    it 'creates a user message' do
      expect {
        service.process_message(content: user_content)
      }.to change { conversation.messages.where(role: 'user').count }.by(1)
    end

    it 'creates an assistant message' do
      expect {
        service.process_message(content: user_content)
      }.to change { conversation.messages.where(role: 'assistant').count }.by(1)
    end

    it 'returns the assistant message' do
      result = service.process_message(content: user_content)
      
      expect(result).to be_a(Message)
      expect(result.role).to eq('assistant')
      expect(result.content).to include('help you with your order')
    end

    context 'when an error occurs' do
      before do
        allow_any_instance_of(Agent).to receive(:generate_response).and_raise(StandardError, 'AI service error')
      end

      it 'creates an error message' do
        result = service.process_message(content: user_content)
        
        expect(result.role).to eq('system')
        expect(result.content).to include('encountered an error')
        expect(result.metadata['error']).to eq('AI service error')
      end
    end

    context 'with handoff detection' do
      let(:ai_response) do
        {
          content: "I understand this is complex. Let me transfer you to a human agent who can better assist you.",
          role: 'assistant',
          finish_reason: 'stop'
        }
      end

      it 'creates a handoff request when detected' do
        expect {
          service.process_message(content: 'This is too complicated')
        }.to change { AgentHandoff.count }.by(1)
      end
    end
  end

  describe '#process_message_stream' do
    let(:user_content) { 'Tell me about your features' }
    let(:response_chunks) { ['Our platform', ' offers many', ' great features!'] }

    before do
      allow_any_instance_of(Agent).to receive(:generate_response_stream).and_yield(response_chunks[0]).and_yield(response_chunks[1]).and_yield(response_chunks[2])
    end

    it 'creates messages and streams response' do
      collected_chunks = []
      
      result = service.process_message_stream(content: user_content) do |chunk, message|
        collected_chunks << chunk
      end
      
      expect(collected_chunks).to eq(response_chunks)
      expect(result.content).to eq(response_chunks.join)
    end

    it 'updates the assistant message progressively' do
      messages_during_stream = []
      
      service.process_message_stream(content: user_content) do |chunk, message|
        messages_during_stream << message.content.dup
      end
      
      expect(messages_during_stream[0]).to eq('Our platform')
      expect(messages_during_stream[1]).to eq('Our platform offers many')
      expect(messages_during_stream[2]).to eq('Our platform offers many great features!')
    end
  end

  describe 'private methods' do
    describe '#build_system_prompt' do
      it 'includes agent information' do
        prompt = service.send(:build_system_prompt)
        
        expect(prompt).to include(agent.name)
        expect(prompt).to include('customer service')
        expect(prompt).to include(tenant.name)
      end

      context 'with different agent types' do
        %w[sales technical_support].each do |agent_kind|
          it "customizes prompt for #{agent_kind} agents" do
            agent.update!(kind: agent_kind)
            prompt = service.send(:build_system_prompt)
            
            expect(prompt).to include(agent_kind.humanize.downcase)
          end
        end
      end
    end

    describe '#build_message_context' do
      before do
        conversation.messages.create!(role: 'user', content: 'First message')
        conversation.messages.create!(role: 'assistant', content: 'First response', sender: agent)
      end

      it 'includes system prompt and recent messages' do
        context = service.send(:build_message_context)
        
        expect(context.first[:role]).to eq('system')
        expect(context.length).to eq(3) # system + 2 messages
      end

      it 'limits to recent 20 messages' do
        25.times do |i|
          conversation.messages.create!(role: 'user', content: "Message #{i}")
        end
        
        context = service.send(:build_message_context)
        # 1 system + 20 recent messages
        expect(context.length).to eq(21)
      end
    end
  end
end