require 'rails_helper'

RSpec.describe Analytics::DashboardMetrics do
  let(:tenant) { create(:tenant) }
  let(:service) { described_class.new(tenant, '7days') }

  describe '#generate' do
    it 'returns all metric categories' do
      metrics = service.generate
      
      expect(metrics).to have_key(:overview)
      expect(metrics).to have_key(:conversation_trends)
      expect(metrics).to have_key(:agent_performance)
      expect(metrics).to have_key(:customer_satisfaction)
      expect(metrics).to have_key(:popular_topics)
      expect(metrics).to have_key(:response_times)
      expect(metrics).to have_key(:resolution_stats)
    end
  end

  describe 'overview metrics' do
    it 'calculates total conversations' do
      create_list(:conversation, 5, tenant: tenant, created_at: 3.days.ago)
      create_list(:conversation, 3, tenant: tenant, created_at: 10.days.ago)
      
      metrics = service.generate
      
      expect(metrics[:overview][:total_conversations]).to eq(5)
    end

    it 'calculates unique customers' do
      customers = create_list(:customer, 3, tenant: tenant)
      customers.each do |customer|
        create(:conversation, tenant: tenant, customer: customer, created_at: 2.days.ago)
      end
      
      metrics = service.generate
      
      expect(metrics[:overview][:unique_customers]).to eq(3)
    end

    it 'calculates resolution rate' do
      create_list(:conversation, 7, tenant: tenant, status: 'resolved', created_at: 2.days.ago)
      create_list(:conversation, 3, tenant: tenant, status: 'active', created_at: 2.days.ago)
      
      metrics = service.generate
      
      expect(metrics[:overview][:resolution_rate]).to eq(70.0)
    end
  end

  describe 'conversation trends' do
    it 'groups conversations by day' do
      (0..6).each do |days_ago|
        create_list(:conversation, 2, tenant: tenant, created_at: days_ago.days.ago)
      end
      
      metrics = service.generate
      trends = metrics[:conversation_trends]
      
      expect(trends[:labels].size).to eq(7)
      expect(trends[:datasets].first[:data]).to all(eq(2))
    end
  end

  describe 'agent performance' do
    it 'includes all tenant agents' do
      agents = create_list(:agent, 3, tenant: tenant)
      
      metrics = service.generate
      
      expect(metrics[:agent_performance].size).to eq(3)
      expect(metrics[:agent_performance].map { |a| a[:id] }).to match_array(agents.map(&:id))
    end

    it 'calculates conversations handled' do
      agent = create(:agent, tenant: tenant)
      create_list(:conversation, 5, tenant: tenant, agent: agent, created_at: 2.days.ago)
      
      metrics = service.generate
      agent_metrics = metrics[:agent_performance].find { |a| a[:id] == agent.id }
      
      expect(agent_metrics[:conversations_handled]).to eq(5)
    end
  end

  describe 'customer satisfaction' do
    it 'categorizes by sentiment' do
      create_list(:conversation_insight, 5, conversation: create(:conversation, tenant: tenant), sentiment: 'positive', created_at: 2.days.ago)
      create_list(:conversation_insight, 3, conversation: create(:conversation, tenant: tenant), sentiment: 'neutral', created_at: 2.days.ago)
      create_list(:conversation_insight, 2, conversation: create(:conversation, tenant: tenant), sentiment: 'negative', created_at: 2.days.ago)
      
      metrics = service.generate
      satisfaction = metrics[:customer_satisfaction]
      
      expect(satisfaction[:satisfied]).to eq(5)
      expect(satisfaction[:neutral]).to eq(3)
      expect(satisfaction[:dissatisfied]).to eq(2)
      expect(satisfaction[:satisfaction_rate]).to eq(50.0)
    end
  end

  describe 'popular topics' do
    it 'extracts and counts topics' do
      create(:conversation_insight, 
        conversation: create(:conversation, tenant: tenant),
        key_topics: ['pricing', 'shipping'],
        created_at: 2.days.ago
      )
      create(:conversation_insight,
        conversation: create(:conversation, tenant: tenant),
        key_topics: ['pricing', 'returns'],
        created_at: 2.days.ago
      )
      
      metrics = service.generate
      topics = metrics[:popular_topics]
      
      pricing_topic = topics.find { |t| t[:topic] == 'pricing' }
      expect(pricing_topic[:count]).to eq(2)
    end
  end

  describe 'response time distribution' do
    it 'categorizes response times' do
      conversation1 = create(:conversation, tenant: tenant, created_at: 1.hour.ago)
      conversation2 = create(:conversation, tenant: tenant, created_at: 1.hour.ago)
      
      create(:message, conversation: conversation1, sender_type: 'agent', created_at: 59.minutes.ago)
      create(:message, conversation: conversation2, sender_type: 'agent', created_at: 50.minutes.ago)
      
      metrics = service.generate
      distribution = metrics[:response_times]
      
      expect(distribution[:under_1_min]).to eq(1)
      expect(distribution[:under_15_min]).to eq(1)
    end
  end

  describe 'resolution statistics' do
    it 'counts conversation statuses' do
      create_list(:conversation, 5, tenant: tenant, status: 'resolved', created_at: 2.days.ago)
      create_list(:conversation, 2, tenant: tenant, status: 'abandoned', created_at: 2.days.ago)
      create_list(:conversation, 3, tenant: tenant, status: 'active', created_at: 2.days.ago)
      
      # Create escalated conversations
      escalated = create_list(:conversation, 2, tenant: tenant, created_at: 2.days.ago)
      escalated.each { |c| create(:agent_handoff, conversation: c) }
      
      metrics = service.generate
      stats = metrics[:resolution_stats]
      
      expect(stats[:resolved]).to eq(5)
      expect(stats[:escalated]).to eq(2)
      expect(stats[:abandoned]).to eq(2)
      expect(stats[:active]).to eq(3)
    end
  end

  describe 'date ranges' do
    it 'respects 24hours range' do
      create(:conversation, tenant: tenant, created_at: 12.hours.ago)
      create(:conversation, tenant: tenant, created_at: 2.days.ago)
      
      service = described_class.new(tenant, '24hours')
      metrics = service.generate
      
      expect(metrics[:overview][:total_conversations]).to eq(1)
    end

    it 'respects 30days range' do
      create(:conversation, tenant: tenant, created_at: 15.days.ago)
      create(:conversation, tenant: tenant, created_at: 45.days.ago)
      
      service = described_class.new(tenant, '30days')
      metrics = service.generate
      
      expect(metrics[:overview][:total_conversations]).to eq(1)
    end
  end
end