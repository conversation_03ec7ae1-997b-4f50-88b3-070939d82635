require 'rails_helper'

RSpec.describe AiService do
  let(:tenant) { create(:tenant) }
  let(:agent) { create(:agent, tenant: tenant, llm_provider: 'openai', llm_model: 'gpt-4o') }
  let(:service) { described_class.new(tenant: tenant, agent: agent) }

  describe '#initialize' do
    it 'initializes with tenant and agent' do
      expect(service.instance_variable_get(:@tenant)).to eq(tenant)
      expect(service.instance_variable_get(:@agent)).to eq(agent)
    end

    it 'determines provider from agent' do
      expect(service.instance_variable_get(:@provider)).to eq(:openai)
    end

    it 'determines model from agent' do
      expect(service.instance_variable_get(:@model)).to eq('gpt-4o')
    end

    context 'without agent llm settings' do
      let(:agent) { create(:agent, tenant: tenant, llm_provider: nil) }

      it 'falls back to default provider' do
        expect(service.instance_variable_get(:@provider)).to eq(:openai)
      end
    end
  end

  describe '#chat' do
    let(:messages) { [{ role: 'user', content: 'Hello, how can you help me?' }] }

    it 'calls the appropriate provider chat method' do
      allow(RubyLLM).to receive(:chat).and_return({
        content: 'I can help you with various tasks!',
        role: 'assistant',
        finish_reason: 'stop',
        usage: { total_tokens: 50 }
      })

      response = service.chat(messages)
      
      expect(response[:content]).to include('help you')
      expect(response[:role]).to eq('assistant')
    end

    context 'with different providers' do
      %w[anthropic google deepseek].each do |provider|
        it "supports #{provider} provider" do
          agent.update!(llm_provider: provider)
          service = described_class.new(agent: agent)

          allow(RubyLLM).to receive(:chat).and_return({
            content: "Response from #{provider}",
            role: 'assistant'
          })

          response = service.chat(messages)
          expect(response[:content]).to include(provider)
        end
      end
    end
  end

  describe '#embeddings' do
    let(:text) { 'Generate embeddings for this text' }

    it 'generates embeddings using the appropriate provider' do
      allow(RubyLLM).to receive(:embeddings).and_return({
        embedding: Array.new(1536) { rand },
        usage: { total_tokens: 10 }
      })

      response = service.embeddings(text)
      
      expect(response[:embedding]).to be_an(Array)
      expect(response[:embedding].length).to eq(1536)
    end

    context 'with provider that does not support embeddings' do
      let(:agent) { create(:agent, tenant: tenant, llm_provider: 'anthropic') }

      it 'falls back to OpenAI embeddings' do
        expect(RubyLLM).to receive(:embeddings).with(
          text,
          provider: :openai,
          model: 'text-embedding-3-small'
        )

        service.embeddings(text)
      end
    end
  end

  describe '#stream_chat' do
    let(:messages) { [{ role: 'user', content: 'Stream this response' }] }

    it 'streams chat responses' do
      chunks = ['Hello', ' there', '!']
      allow(RubyLLM).to receive(:chat).and_yield(chunks[0]).and_yield(chunks[1]).and_yield(chunks[2])

      collected_chunks = []
      service.stream_chat(messages) { |chunk| collected_chunks << chunk }

      expect(collected_chunks).to eq(chunks)
    end
  end

  describe '#available_models' do
    it 'returns models for the current provider' do
      models = service.available_models
      
      expect(models).to include('gpt-4o')
      expect(models).to include('gpt-4o-mini')
    end

    context 'with different providers' do
      it 'returns Anthropic models' do
        agent.update!(llm_provider: 'anthropic')
        service = described_class.new(agent: agent)
        
        models = service.available_models
        expect(models).to include('claude-3-opus-20240229')
      end

      it 'returns Google models' do
        agent.update!(llm_provider: 'google')
        service = described_class.new(agent: agent)
        
        models = service.available_models
        expect(models).to include('gemini-1.5-pro')
      end
    end
  end
end