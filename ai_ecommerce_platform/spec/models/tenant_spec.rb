require 'rails_helper'

RSpec.describe Tenant, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:subdomain) }
    it { should validate_uniqueness_of(:subdomain) }
    it { should validate_uniqueness_of(:custom_domain).allow_blank }
    
    it 'validates subdomain format' do
      tenant = Tenant.new(name: 'Test', subdomain: 'test-123')
      expect(tenant).to be_valid
      
      tenant.subdomain = 'Test_123'
      expect(tenant).not_to be_valid
      expect(tenant.errors[:subdomain]).to include('only allows lowercase letters, numbers, and hyphens')
    end
  end
  
  describe 'associations' do
    it { should have_many(:tenant_users).dependent(:destroy) }
    it { should have_many(:users).through(:tenant_users) }
    it { should have_many(:conversations).dependent(:destroy) }
    it { should have_many(:agents).dependent(:destroy) }
    it { should have_many(:integrations).dependent(:destroy) }
    it { should have_many(:knowledge_sources).dependent(:destroy) }
    it { should have_many(:messages).through(:conversations) }
    it { should have_many(:conversation_insights).through(:conversations) }
  end
  
  describe 'enums' do
    it { should define_enum_for(:status).with_values(active: 0, suspended: 1, cancelled: 2, trial: 3).with_default(:trial) }
  end
  
  describe 'default settings' do
    it 'sets default settings on initialization' do
      tenant = Tenant.new
      expect(tenant.settings).to include(
        'max_conversations' => 1000,
        'max_agents' => 10,
        'features' => ['chat', 'email', 'analytics'],
        'language' => 'en'
      )
    end
  end
end
