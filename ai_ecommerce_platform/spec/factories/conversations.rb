FactoryBot.define do
  factory :conversation do
    association :tenant
    customer_email { Faker::Internet.email }
    customer_name { Faker::Name.name }
    channel { 'web' }
    status { 'active' }
    metadata { {} }
    started_at { Time.current }
    
    trait :with_agent do
      association :assigned_agent, factory: :agent
    end
    
    trait :resolved do
      status { 'resolved' }
      ended_at { Time.current }
    end
    
    trait :escalated do
      status { 'escalated' }
    end
  end
end
