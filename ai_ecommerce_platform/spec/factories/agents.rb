FactoryBot.define do
  factory :agent do
    name { Faker::Name.name }
    kind { 'customer_service' }
    llm_provider { 'openai' }
    description { 'AI customer service agent' }
    capabilities { ['order_inquiry', 'product_questions'] }
    settings { {
      'max_concurrent_conversations' => 10,
      'response_style' => 'professional',
      'temperature' => 0.7,
      'max_tokens' => 500
    } }
    status { 'active' }
    association :tenant
  end
end
