FactoryBot.define do
  factory :message do
    association :conversation
    sender_type { 'customer' }
    content { Faker::Lorem.paragraph }
    metadata { {} }
    
    trait :from_agent do
      sender_type { 'agent' }
      sender_id { create(:agent).id }
      tokens_used { rand(10..100) }
    end
    
    trait :from_customer do
      sender_type { 'customer' }
    end
    
    trait :with_embedding do
      embedding_generated { true }
    end
  end
end
