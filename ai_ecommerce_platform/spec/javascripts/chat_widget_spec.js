describe('AIChatWidget', () => {
  let widget;
  let fetchMock;

  beforeEach(() => {
    // Set up DOM
    document.body.innerHTML = '';
    
    // Mock fetch
    fetchMock = jasmine.createSpy('fetch');
    window.fetch = fetchMock;
    
    // Mock localStorage
    spyOn(localStorage, 'getItem').and.returnValue(null);
    spyOn(localStorage, 'setItem');
  });

  afterEach(() => {
    // Clean up
    document.body.innerHTML = '';
  });

  describe('initialization', () => {
    it('creates widget with provided config', () => {
      const config = {
        apiKey: 'test-api-key',
        customerEmail: '<EMAIL>',
        customerName: 'Test User'
      };

      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [{ name: 'AI Assistant', greeting: 'Hello!' }],
          widget_settings: { primary_color: '#0066CC' }
        })
      }));

      widget = new AIChatWidget(config);

      expect(widget.apiKey).toBe('test-api-key');
      expect(widget.config.customerEmail).toBe('<EMAIL>');
    });

    it('generates and stores session ID', () => {
      const config = { apiKey: 'test-api-key' };
      
      widget = new AIChatWidget(config);
      
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'ai_chat_session_id',
        jasmine.any(String)
      );
    });

    it('fetches widget configuration on init', (done) => {
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [],
          widget_settings: {}
        })
      }));

      widget = new AIChatWidget({ apiKey: 'test-api-key' });

      setTimeout(() => {
        expect(fetchMock).toHaveBeenCalledWith(
          jasmine.stringContaining('/widget/config?api_key=test-api-key')
        );
        done();
      }, 100);
    });
  });

  describe('UI interactions', () => {
    beforeEach((done) => {
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [{ name: 'AI', greeting: 'Hello!' }],
          widget_settings: { primary_color: '#0066CC' }
        })
      }));

      widget = new AIChatWidget({ apiKey: 'test-api-key' });
      
      setTimeout(done, 100); // Wait for initialization
    });

    it('toggles chat window on button click', () => {
      const chatButton = document.querySelector('.ai-chat-button');
      const chatWindow = document.querySelector('.ai-chat-window');
      
      expect(chatWindow.classList.contains('open')).toBe(false);
      
      chatButton.click();
      expect(chatWindow.classList.contains('open')).toBe(true);
      
      chatButton.click();
      expect(chatWindow.classList.contains('open')).toBe(false);
    });

    it('closes chat window on close button click', () => {
      const chatButton = document.querySelector('.ai-chat-button');
      const closeButton = document.querySelector('.ai-chat-close');
      const chatWindow = document.querySelector('.ai-chat-window');
      
      chatButton.click(); // Open chat
      expect(chatWindow.classList.contains('open')).toBe(true);
      
      closeButton.click();
      expect(chatWindow.classList.contains('open')).toBe(false);
    });

    it('shows greeting message on first open', () => {
      const chatButton = document.querySelector('.ai-chat-button');
      chatButton.click();
      
      const messages = document.querySelectorAll('.ai-message');
      expect(messages.length).toBe(1);
      expect(messages[0].textContent).toContain('Hello!');
    });
  });

  describe('message sending', () => {
    beforeEach((done) => {
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [],
          widget_settings: {}
        })
      }));

      widget = new AIChatWidget({ apiKey: 'test-api-key' });
      setTimeout(done, 100);
    });

    it('sends message on form submit', (done) => {
      const form = document.getElementById('ai-chat-form');
      const input = document.getElementById('ai-chat-input');
      
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          conversation_id: '123',
          message_id: '456',
          status: 'processing'
        })
      }));
      
      input.value = 'Test message';
      form.dispatchEvent(new Event('submit'));
      
      setTimeout(() => {
        expect(fetchMock).toHaveBeenCalledWith(
          jasmine.stringContaining('/chat'),
          jasmine.objectContaining({
            method: 'POST',
            headers: jasmine.objectContaining({
              'Content-Type': 'application/json',
              'X-API-Key': 'test-api-key'
            })
          })
        );
        done();
      }, 100);
    });

    it('adds user message to chat', () => {
      const form = document.getElementById('ai-chat-form');
      const input = document.getElementById('ai-chat-input');
      
      input.value = 'Test message';
      form.dispatchEvent(new Event('submit'));
      
      const messages = document.querySelectorAll('.ai-message.user');
      expect(messages.length).toBe(1);
      expect(messages[0].textContent).toContain('Test message');
    });

    it('shows typing indicator while waiting for response', () => {
      const form = document.getElementById('ai-chat-form');
      const input = document.getElementById('ai-chat-input');
      
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ conversation_id: '123' })
      }));
      
      input.value = 'Test message';
      form.dispatchEvent(new Event('submit'));
      
      const typingIndicator = document.getElementById('typing-indicator');
      expect(typingIndicator).toBeTruthy();
    });
  });

  describe('event tracking', () => {
    beforeEach((done) => {
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [],
          widget_settings: {}
        })
      }));

      widget = new AIChatWidget({ apiKey: 'test-api-key' });
      setTimeout(done, 100);
    });

    it('tracks widget open event', () => {
      fetchMock.calls.reset();
      
      const chatButton = document.querySelector('.ai-chat-button');
      chatButton.click();
      
      expect(fetchMock).toHaveBeenCalledWith(
        jasmine.stringContaining('/widget/events'),
        jasmine.objectContaining({
          method: 'POST',
          body: jasmine.stringContaining('"event_type":"widget_opened"')
        })
      );
    });

    it('tracks widget close event', () => {
      const chatButton = document.querySelector('.ai-chat-button');
      const closeButton = document.querySelector('.ai-chat-close');
      
      chatButton.click(); // Open first
      fetchMock.calls.reset();
      
      closeButton.click();
      
      expect(fetchMock).toHaveBeenCalledWith(
        jasmine.stringContaining('/widget/events'),
        jasmine.objectContaining({
          method: 'POST',
          body: jasmine.stringContaining('"event_type":"widget_closed"')
        })
      );
    });

    it('tracks page view on initialization', () => {
      expect(fetchMock).toHaveBeenCalledWith(
        jasmine.stringContaining('/widget/events'),
        jasmine.objectContaining({
          method: 'POST',
          body: jasmine.stringContaining('"event_type":"page_view"')
        })
      );
    });
  });

  describe('error handling', () => {
    it('handles API errors gracefully', (done) => {
      fetchMock.and.returnValue(Promise.reject(new Error('Network error')));
      
      widget = new AIChatWidget({ apiKey: 'test-api-key' });
      
      setTimeout(() => {
        // Widget should still be created even if config fetch fails
        expect(document.getElementById('ai-chat-widget')).toBeTruthy();
        done();
      }, 100);
    });

    it('shows error message when message send fails', (done) => {
      fetchMock.and.returnValue(Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          tenant: { name: 'Test Company' },
          agents: [],
          widget_settings: {}
        })
      }));

      widget = new AIChatWidget({ apiKey: 'test-api-key' });
      
      setTimeout(() => {
        const form = document.getElementById('ai-chat-form');
        const input = document.getElementById('ai-chat-input');
        
        fetchMock.and.returnValue(Promise.reject(new Error('Send failed')));
        
        input.value = 'Test message';
        form.dispatchEvent(new Event('submit'));
        
        setTimeout(() => {
          const messages = document.querySelectorAll('.ai-message');
          const lastMessage = messages[messages.length - 1];
          expect(lastMessage.textContent).toContain('Sorry, I encountered an error');
          done();
        }, 100);
      }, 100);
    });
  });
});