require 'rails_helper'

RSpec.describe Api::V1::ChatController, type: :controller do
  let(:tenant) { create(:tenant, api_key: 'test-api-key', active: true) }
  
  before do
    request.headers['X-API-Key'] = 'test-api-key'
  end

  describe 'POST #create' do
    let(:valid_params) do
      {
        message: 'Hello, I need help',
        session_id: 'test-session-123',
        page_url: 'https://example.com/products',
        customer_email: '<EMAIL>',
        customer_name: '<PERSON>'
      }
    end

    it 'creates a new conversation if none exists' do
      expect {
        post :create, params: valid_params
      }.to change { Conversation.count }.by(1)
    end

    it 'creates a new message' do
      expect {
        post :create, params: valid_params
      }.to change { Message.count }.by(1)
    end

    it 'enqueues ProcessMessageJob' do
      expect {
        post :create, params: valid_params
      }.to have_enqueued_job(ProcessMessageJob)
    end

    it 'returns conversation and message ids' do
      post :create, params: valid_params
      
      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('conversation_id')
      expect(json_response).to have_key('message_id')
      expect(json_response['status']).to eq('processing')
    end

    it 'creates customer if email provided' do
      expect {
        post :create, params: valid_params
      }.to change { Customer.count }.by(1)
      
      customer = Customer.last
      expect(customer.email).to eq('<EMAIL>')
      expect(customer.name).to eq('John Doe')
    end

    context 'with existing conversation' do
      let!(:conversation) { create(:conversation, tenant: tenant, session_id: 'test-session-123') }

      it 'does not create new conversation' do
        expect {
          post :create, params: valid_params
        }.not_to change { Conversation.count }
      end

      it 'adds message to existing conversation' do
        post :create, params: valid_params
        
        expect(conversation.messages.count).to eq(1)
      end
    end
  end

  describe 'GET #show' do
    let(:conversation) { create(:conversation, tenant: tenant, session_id: 'test-session-123') }
    let!(:messages) { create_list(:message, 3, conversation: conversation) }

    it 'returns conversation with messages' do
      get :show, params: { session_id: 'test-session-123' }
      
      json_response = JSON.parse(response.body)
      expect(json_response['conversation']['messages'].count).to eq(3)
    end

    it 'returns 404 for non-existent conversation' do
      get :show, params: { session_id: 'non-existent' }
      
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'GET #messages' do
    let(:conversation) { create(:conversation, tenant: tenant, session_id: 'test-session-123') }
    let!(:old_message) { create(:message, conversation: conversation, created_at: 1.hour.ago) }
    let!(:new_message) { create(:message, conversation: conversation, created_at: 1.minute.ago) }

    it 'returns all messages' do
      get :messages, params: { session_id: 'test-session-123' }
      
      json_response = JSON.parse(response.body)
      expect(json_response['messages'].count).to eq(2)
    end

    it 'filters messages after timestamp' do
      get :messages, params: { 
        session_id: 'test-session-123',
        after: 30.minutes.ago.iso8601
      }
      
      json_response = JSON.parse(response.body)
      expect(json_response['messages'].count).to eq(1)
      expect(json_response['messages'].first['id']).to eq(new_message.id)
    end
  end

  describe 'authentication' do
    context 'with invalid API key' do
      before { request.headers['X-API-Key'] = 'invalid-key' }

      it 'returns unauthorized' do
        post :create, params: { message: 'Hello' }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with inactive tenant' do
      before do
        tenant.update!(active: false)
      end

      it 'returns unauthorized' do
        post :create, params: { message: 'Hello' }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end