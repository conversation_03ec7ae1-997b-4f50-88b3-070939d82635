require 'rails_helper'

RSpec.describe AgentsController, type: :controller do
  let(:user) { create(:user) }
  let(:tenant) { create(:tenant) }
  let(:tenant_user) { create(:tenant_user, user: user, tenant: tenant, role: create(:role, name: 'admin')) }
  let(:agent) { create(:agent, tenant: tenant) }

  before do
    sign_in user
    allow(Current).to receive(:tenant).and_return(tenant)
    allow(controller).to receive(:current_tenant).and_return(tenant)
  end

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'assigns agents' do
      agents = create_list(:agent, 3, tenant: tenant)
      
      get :index
      
      expect(assigns(:agents)).to match_array(agents)
    end
  end

  describe 'GET #show' do
    it 'returns http success' do
      get :show, params: { id: agent.id }
      expect(response).to have_http_status(:success)
    end

    it 'assigns agent and metrics' do
      get :show, params: { id: agent.id }
      
      expect(assigns(:agent)).to eq(agent)
      expect(assigns(:performance_metrics)).to be_present
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end

    it 'builds a new agent' do
      get :new
      
      expect(assigns(:agent)).to be_a_new(Agent)
      expect(assigns(:agent).tenant).to eq(tenant)
    end
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        name: 'Test Agent',
        role: 'support',
        personality: 'Friendly and helpful',
        greeting_message: 'Hello! How can I help?',
        model_provider: 'openai',
        model_name: 'gpt-4',
        temperature: 0.7,
        max_tokens: 500
      }
    end

    context 'with valid params' do
      it 'creates a new Agent' do
        expect {
          post :create, params: { agent: valid_attributes }
        }.to change(Agent, :count).by(1)
      end

      it 'redirects to the created agent' do
        post :create, params: { agent: valid_attributes }
        expect(response).to redirect_to(Agent.last)
      end
    end

    context 'with invalid params' do
      it 'does not create a new Agent' do
        expect {
          post :create, params: { agent: { name: '' } }
        }.not_to change(Agent, :count)
      end

      it 'renders new template' do
        post :create, params: { agent: { name: '' } }
        expect(response).to render_template(:new)
      end
    end
  end

  describe 'PUT #update' do
    let(:new_attributes) do
      { name: 'Updated Agent Name' }
    end

    context 'with valid params' do
      it 'updates the agent' do
        put :update, params: { id: agent.id, agent: new_attributes }
        
        agent.reload
        expect(agent.name).to eq('Updated Agent Name')
      end

      it 'redirects to the agent' do
        put :update, params: { id: agent.id, agent: new_attributes }
        expect(response).to redirect_to(agent)
      end
    end
  end

  describe 'DELETE #destroy' do
    it 'destroys the agent' do
      agent # create agent before expect block
      
      expect {
        delete :destroy, params: { id: agent.id }
      }.to change(Agent, :count).by(-1)
    end

    it 'redirects to agents list' do
      delete :destroy, params: { id: agent.id }
      expect(response).to redirect_to(agents_url)
    end
  end

  describe 'POST #test' do
    let(:test_service) { instance_double(Ai::TestService) }

    before do
      allow(Ai::TestService).to receive(:new).and_return(test_service)
    end

    it 'tests agent response' do
      allow(test_service).to receive(:test_response).and_return('Test response')
      
      post :test, params: { id: agent.id, test_message: 'Hello' }, format: :json
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['response']).to eq('Test response')
    end

    it 'handles errors gracefully' do
      allow(test_service).to receive(:test_response).and_raise(StandardError.new('API Error'))
      
      post :test, params: { id: agent.id, test_message: 'Hello' }, format: :json
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be false
      expect(json_response['error']).to eq('API Error')
    end
  end
end