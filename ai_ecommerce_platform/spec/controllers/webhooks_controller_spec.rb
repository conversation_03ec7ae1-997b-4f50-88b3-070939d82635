require 'rails_helper'

RSpec.describe WebhooksController, type: :controller do
  let(:tenant) { create(:tenant) }
  let(:integration) { create(:integration, tenant: tenant, platform: 'shopify', webhook_token: 'test-webhook-token') }

  describe 'POST #shopify' do
    let(:webhook_payload) do
      {
        id: 12345,
        title: 'New Product',
        variants: [{ price: '49.99', sku: 'NEW-001' }]
      }
    end

    before do
      allow_any_instance_of(Ecommerce::ShopifyIntegration).to receive(:webhook_handler)
        .and_return({ success: true })
    end

    context 'in development environment' do
      before { allow(Rails.env).to receive(:development?).and_return(true) }

      it 'processes webhook successfully' do
        request.headers['X-Shopify-Topic'] = 'products/create'
        
        post :shopify, params: webhook_payload.merge(
          platform: 'shopify',
          token: 'test-webhook-token'
        ), as: :json
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with valid webhook signature' do
      before do
        allow(Rails.env).to receive(:development?).and_return(false)
        
        integration.config['webhook_secret'] = 'test-secret'
        integration.save!
        
        hmac = Base64.strict_encode64(
          OpenSSL::HMAC.digest('sha256', 'test-secret', webhook_payload.to_json)
        )
        request.headers['X-Shopify-Hmac-Sha256'] = hmac
        request.headers['X-Shopify-Topic'] = 'products/create'
      end

      it 'processes webhook successfully' do
        post :shopify, params: webhook_payload.merge(
          platform: 'shopify',
          token: 'test-webhook-token'
        ), as: :json
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid webhook token' do
      it 'returns not found' do
        post :shopify, params: webhook_payload.merge(
          platform: 'shopify',
          token: 'invalid-token'
        ), as: :json
        
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when webhook processing fails' do
      before do
        allow(Rails.env).to receive(:development?).and_return(true)
        allow_any_instance_of(Ecommerce::ShopifyIntegration).to receive(:webhook_handler)
          .and_return({ success: false, error: 'Processing failed' })
      end

      it 'returns unprocessable entity' do
        post :shopify, params: webhook_payload.merge(
          platform: 'shopify',
          token: 'test-webhook-token'
        ), as: :json
        
        expect(response).to have_http_status(:unprocessable_entity)
        
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Processing failed')
      end
    end
  end

  describe 'POST #woocommerce' do
    let(:integration) { create(:integration, tenant: tenant, platform: 'woocommerce', webhook_token: 'woo-webhook-token') }
    let(:webhook_payload) do
      {
        event: 'product.created',
        resource: 'product',
        data: { id: 789, name: 'WooCommerce Product' }
      }
    end

    before do
      allow_any_instance_of(Ecommerce::WoocommerceIntegration).to receive(:webhook_handler)
        .and_return({ success: true })
      allow(Rails.env).to receive(:development?).and_return(true)
    end

    it 'processes webhook successfully' do
      request.headers['X-WC-Webhook-Topic'] = 'product.created'
      
      post :woocommerce, params: webhook_payload.merge(
        platform: 'woocommerce',
        token: 'woo-webhook-token'
      ), as: :json
      
      expect(response).to have_http_status(:ok)
    end
  end
end