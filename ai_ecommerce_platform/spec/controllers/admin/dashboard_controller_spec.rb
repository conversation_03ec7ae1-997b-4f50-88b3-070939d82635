require 'rails_helper'

RSpec.describe Admin::DashboardController, type: :controller do
  let(:admin_user) { create(:user, admin: true) }
  let(:regular_user) { create(:user, admin: false) }

  describe 'GET #index' do
    context 'when user is admin' do
      before { sign_in admin_user }

      it 'returns http success' do
        get :index
        expect(response).to have_http_status(:success)
      end

      it 'assigns dashboard metrics' do
        create_list(:tenant, 3)
        create_list(:user, 5)
        create_list(:conversation, 7)
        create_list(:message, 10)

        get :index
        
        expect(assigns(:total_tenants)).to eq(3)
        expect(assigns(:total_users)).to eq(6) # 5 + admin_user
        expect(assigns(:total_conversations)).to eq(7)
        expect(assigns(:total_messages)).to eq(10)
      end
    end

    context 'when user is not admin' do
      before { sign_in regular_user }

      it 'redirects to root path' do
        get :index
        expect(response).to redirect_to(root_path)
      end
    end

    context 'when user is not authenticated' do
      it 'redirects to sign in' do
        get :index
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end
end