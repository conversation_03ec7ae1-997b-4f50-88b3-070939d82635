require 'rails_helper'

RSpec.describe DashboardController, type: :controller do
  let(:user) { create(:user) }
  let(:tenant) { create(:tenant) }
  let(:tenant_user) { create(:tenant_user, user: user, tenant: tenant, role: create(:role, name: 'admin')) }

  before do
    sign_in user
    allow(Current).to receive(:tenant).and_return(tenant)
    allow(controller).to receive(:current_tenant).and_return(tenant)
  end

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'assigns dashboard metrics' do
      create_list(:conversation, 3, tenant: tenant, created_at: Date.current)
      create_list(:conversation, 2, tenant: tenant, status: 'active')
      create_list(:message, 5, conversation: create(:conversation, tenant: tenant), created_at: Date.current)
      
      get :index
      
      expect(assigns(:conversations_today)).to eq(3)
      expect(assigns(:active_conversations)).to eq(2)
      expect(assigns(:total_messages)).to eq(5)
    end

    it 'calculates average response time' do
      conversation = create(:conversation, tenant: tenant, created_at: 1.hour.ago)
      create(:message, conversation: conversation, sender_type: 'agent', created_at: 55.minutes.ago)
      
      get :index
      
      expect(assigns(:response_time)).to be > 0
    end

    it 'loads recent conversations' do
      conversations = create_list(:conversation, 10, tenant: tenant)
      
      get :index
      
      expect(assigns(:recent_conversations).size).to eq(5)
      expect(assigns(:recent_conversations)).to match_array(conversations.last(5))
    end

    it 'loads top performing agents' do
      agents = create_list(:agent, 5, tenant: tenant)
      agents.each_with_index do |agent, i|
        create_list(:conversation, i + 1, tenant: tenant, agent: agent)
      end
      
      get :index
      
      expect(assigns(:top_agents).size).to eq(3)
      expect(assigns(:top_agents).first.conversations.count).to be >= assigns(:top_agents).last.conversations.count
    end
  end

  describe 'GET #analytics' do
    it 'returns http success' do
      get :analytics
      expect(response).to have_http_status(:success)
    end

    it 'accepts date range parameter' do
      get :analytics, params: { date_range: '30days' }
      
      expect(assigns(:date_range)).to eq('30days')
    end

    it 'generates analytics metrics' do
      service = instance_double(Analytics::DashboardMetrics)
      allow(Analytics::DashboardMetrics).to receive(:new).and_return(service)
      allow(service).to receive(:generate).and_return({})
      
      get :analytics
      
      expect(Analytics::DashboardMetrics).to have_received(:new).with(tenant, '7days')
      expect(service).to have_received(:generate)
    end
  end
end