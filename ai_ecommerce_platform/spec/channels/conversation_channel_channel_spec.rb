require 'rails_helper'

RSpec.describe ConversationChannel, type: :channel do
  let(:user) { create(:user) }
  let(:tenant) { create(:tenant) }
  let(:conversation) { create(:conversation, tenant: tenant) }

  before do
    stub_connection current_user: user, current_tenant: tenant
  end

  describe '#subscribed' do
    it 'subscribes to a stream when conversation id is provided' do
      subscribe(conversation_id: conversation.id)
      
      expect(subscription).to be_confirmed
      expect(subscription).to have_stream_for(conversation)
    end

    it 'rejects when no conversation id is provided' do
      subscribe
      
      expect(subscription).to be_rejected
    end
  end

  describe '#unsubscribed' do
    it 'unsubscribes from the stream' do
      subscribe(conversation_id: conversation.id)
      
      expect { unsubscribe }.not_to raise_error
    end
  end

  describe '#speak' do
    before do
      subscribe(conversation_id: conversation.id)
    end

    it 'creates a new message' do
      expect {
        perform :speak, message: 'Hello world', sender_type: 'customer'
      }.to change { Message.count }.by(1)
    end

    it 'broadcasts the message' do
      expect {
        perform :speak, message: 'Hello world', sender_type: 'customer'
      }.to have_broadcasted_to(conversation).with { |data|
        expect(data[:message]).to include('Hello world')
      }
    end
  end
end
