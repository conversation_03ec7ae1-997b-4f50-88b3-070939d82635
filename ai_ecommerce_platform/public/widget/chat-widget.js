(function() {
  'use strict';

  // Widget configuration
  const WIDGET_ID = 'ai-chat-widget';
  const API_BASE_URL = 'https://api.yourdomain.com/api/v1';
  
  class AIChatWidget {
    constructor(config) {
      this.config = config;
      this.apiKey = config.apiKey;
      this.sessionId = this.getOrCreateSessionId();
      this.isOpen = false;
      this.messages = [];
      this.widgetConfig = null;
      
      this.init();
    }

    async init() {
      try {
        // Fetch widget configuration
        await this.fetchWidgetConfig();
        
        // Inject styles
        this.injectStyles();
        
        // Create widget HTML
        this.createWidget();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load existing conversation if any
        await this.loadConversation();
        
        // Track page view
        this.trackEvent('page_view', {
          page_url: window.location.href,
          page_title: document.title
        });
      } catch (error) {
        console.error('Failed to initialize chat widget:', error);
      }
    }

    async fetchWidgetConfig() {
      const response = await fetch(`${API_BASE_URL}/widget/config?api_key=${this.apiKey}`);
      if (!response.ok) throw new Error('Failed to fetch widget configuration');
      
      this.widgetConfig = await response.json();
    }

    injectStyles() {
      const styles = `
        #${WIDGET_ID} {
          position: fixed;
          bottom: 20px;
          right: 20px;
          z-index: 9999;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        .ai-chat-button {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background-color: ${this.widgetConfig.widget_settings.primary_color || '#0066CC'};
          color: white;
          border: none;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          transition: transform 0.2s;
        }

        .ai-chat-button:hover {
          transform: scale(1.1);
        }

        .ai-chat-window {
          position: absolute;
          bottom: 80px;
          right: 0;
          width: 380px;
          height: 600px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 5px 40px rgba(0, 0, 0, 0.15);
          display: none;
          flex-direction: column;
          overflow: hidden;
        }

        .ai-chat-window.open {
          display: flex;
        }

        .ai-chat-header {
          background-color: ${this.widgetConfig.widget_settings.primary_color || '#0066CC'};
          color: white;
          padding: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .ai-chat-header-info {
          display: flex;
          align-items: center;
        }

        .ai-chat-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-weight: bold;
        }

        .ai-chat-close {
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          padding: 4px;
        }

        .ai-chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 20px;
          background-color: #f8f9fa;
        }

        .ai-message {
          margin-bottom: 16px;
          display: flex;
          align-items: flex-start;
        }

        .ai-message.user {
          flex-direction: row-reverse;
        }

        .ai-message-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #e0e0e0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 8px;
          font-size: 12px;
          font-weight: bold;
        }

        .ai-message.user .ai-message-avatar {
          background-color: ${this.widgetConfig.widget_settings.primary_color || '#0066CC'};
          color: white;
        }

        .ai-message-content {
          max-width: 70%;
          padding: 12px 16px;
          border-radius: 18px;
          background-color: white;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .ai-message.user .ai-message-content {
          background-color: ${this.widgetConfig.widget_settings.primary_color || '#0066CC'};
          color: white;
        }

        .ai-message-time {
          font-size: 11px;
          color: #666;
          margin-top: 4px;
        }

        .ai-chat-input {
          padding: 20px;
          background-color: white;
          border-top: 1px solid #e0e0e0;
        }

        .ai-chat-input-wrapper {
          display: flex;
          align-items: center;
          background-color: #f8f9fa;
          border-radius: 24px;
          padding: 4px;
        }

        .ai-chat-input-field {
          flex: 1;
          border: none;
          background: none;
          padding: 12px 16px;
          font-size: 14px;
          outline: none;
        }

        .ai-chat-send {
          background-color: ${this.widgetConfig.widget_settings.primary_color || '#0066CC'};
          color: white;
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: opacity 0.2s;
        }

        .ai-chat-send:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .ai-typing-indicator {
          display: flex;
          align-items: center;
          padding: 12px 16px;
        }

        .ai-typing-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #999;
          margin: 0 2px;
          animation: typing 1.4s infinite;
        }

        .ai-typing-dot:nth-child(2) {
          animation-delay: 0.2s;
        }

        .ai-typing-dot:nth-child(3) {
          animation-delay: 0.4s;
        }

        @keyframes typing {
          0%, 60%, 100% {
            opacity: 0.3;
          }
          30% {
            opacity: 1;
          }
        }

        @media (max-width: 420px) {
          .ai-chat-window {
            width: 100vw;
            height: 100vh;
            bottom: 0;
            right: 0;
            border-radius: 0;
          }
        }
      `;

      const styleSheet = document.createElement('style');
      styleSheet.textContent = styles;
      document.head.appendChild(styleSheet);
    }

    createWidget() {
      const widgetHTML = `
        <div id="${WIDGET_ID}">
          <button class="ai-chat-button" aria-label="Open chat">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          </button>
          
          <div class="ai-chat-window">
            <div class="ai-chat-header">
              <div class="ai-chat-header-info">
                <div class="ai-chat-avatar">AI</div>
                <div>
                  <div style="font-weight: 600;">${this.widgetConfig.tenant.name}</div>
                  <div style="font-size: 12px; opacity: 0.9;">We typically reply instantly</div>
                </div>
              </div>
              <button class="ai-chat-close" aria-label="Close chat">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            
            <div class="ai-chat-messages" id="ai-chat-messages"></div>
            
            <div class="ai-chat-input">
              <form class="ai-chat-input-wrapper" id="ai-chat-form">
                <input 
                  type="text" 
                  class="ai-chat-input-field" 
                  placeholder="Type your message..."
                  id="ai-chat-input"
                />
                <button type="submit" class="ai-chat-send" id="ai-chat-send">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                  </svg>
                </button>
              </form>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', widgetHTML);
    }

    setupEventListeners() {
      const chatButton = document.querySelector('.ai-chat-button');
      const closeButton = document.querySelector('.ai-chat-close');
      const chatForm = document.getElementById('ai-chat-form');
      const chatInput = document.getElementById('ai-chat-input');

      chatButton.addEventListener('click', () => this.toggleChat());
      closeButton.addEventListener('click', () => this.closeChat());
      
      chatForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          await this.sendMessage(message);
          chatInput.value = '';
        }
      });

      // Auto-open based on configuration
      if (this.widgetConfig.widget_settings.auto_open_delay) {
        setTimeout(() => {
          if (!this.isOpen) {
            this.openChat();
          }
        }, this.widgetConfig.widget_settings.auto_open_delay * 1000);
      }
    }

    toggleChat() {
      this.isOpen ? this.closeChat() : this.openChat();
    }

    openChat() {
      const chatWindow = document.querySelector('.ai-chat-window');
      chatWindow.classList.add('open');
      this.isOpen = true;
      
      this.trackEvent('widget_opened');
      
      // Show greeting if first time
      if (this.messages.length === 0 && this.widgetConfig.agents.length > 0) {
        this.addMessage('agent', this.widgetConfig.agents[0].greeting, {
          agent_name: this.widgetConfig.agents[0].name
        });
      }
    }

    closeChat() {
      const chatWindow = document.querySelector('.ai-chat-window');
      chatWindow.classList.remove('open');
      this.isOpen = false;
      
      this.trackEvent('widget_closed');
    }

    async loadConversation() {
      try {
        const response = await fetch(`${API_BASE_URL}/chat/${this.sessionId}`, {
          headers: {
            'X-API-Key': this.apiKey
          }
        });

        if (response.ok) {
          const data = await response.json();
          data.conversation.messages.forEach(msg => {
            this.addMessage(
              msg.sender_type === 'customer' ? 'user' : 'agent',
              msg.content,
              { time: new Date(msg.created_at) }
            );
          });
        }
      } catch (error) {
        console.error('Failed to load conversation:', error);
      }
    }

    async sendMessage(message) {
      // Add user message
      this.addMessage('user', message);
      
      // Show typing indicator
      this.showTypingIndicator();
      
      try {
        const response = await fetch(`${API_BASE_URL}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          },
          body: JSON.stringify({
            message: message,
            session_id: this.sessionId,
            page_url: window.location.href,
            referrer: document.referrer,
            customer_email: this.config.customerEmail,
            customer_name: this.config.customerName
          })
        });

        if (!response.ok) throw new Error('Failed to send message');

        const data = await response.json();
        
        // Poll for response
        this.pollForResponse(data.conversation_id);
        
        this.trackEvent('message_sent', { message_length: message.length });
      } catch (error) {
        console.error('Failed to send message:', error);
        this.hideTypingIndicator();
        this.addMessage('agent', 'Sorry, I encountered an error. Please try again.');
      }
    }

    async pollForResponse(conversationId) {
      const pollInterval = setInterval(async () => {
        try {
          const response = await fetch(`${API_BASE_URL}/chat/${this.sessionId}/messages?after=${new Date().toISOString()}`, {
            headers: {
              'X-API-Key': this.apiKey
            }
          });

          if (response.ok) {
            const data = await response.json();
            const agentMessages = data.messages.filter(m => m.sender_type === 'agent');
            
            if (agentMessages.length > 0) {
              clearInterval(pollInterval);
              this.hideTypingIndicator();
              
              agentMessages.forEach(msg => {
                this.addMessage('agent', msg.content, {
                  agent_name: msg.sender_name,
                  time: new Date(msg.created_at)
                });
              });
            }
          }
        } catch (error) {
          console.error('Polling error:', error);
        }
      }, 1000);

      // Stop polling after 30 seconds
      setTimeout(() => {
        clearInterval(pollInterval);
        this.hideTypingIndicator();
      }, 30000);
    }

    addMessage(sender, content, metadata = {}) {
      const messagesContainer = document.getElementById('ai-chat-messages');
      const messageId = `msg-${Date.now()}`;
      
      const messageHTML = `
        <div class="ai-message ${sender}" id="${messageId}">
          <div class="ai-message-avatar">
            ${sender === 'user' ? 'You' : (metadata.agent_name || 'AI').substring(0, 2).toUpperCase()}
          </div>
          <div>
            <div class="ai-message-content">${this.escapeHtml(content)}</div>
            <div class="ai-message-time">${this.formatTime(metadata.time || new Date())}</div>
          </div>
        </div>
      `;

      messagesContainer.insertAdjacentHTML('beforeend', messageHTML);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
      
      this.messages.push({ sender, content, metadata });
    }

    showTypingIndicator() {
      const messagesContainer = document.getElementById('ai-chat-messages');
      const indicatorHTML = `
        <div class="ai-typing-indicator" id="typing-indicator">
          <div class="ai-typing-dot"></div>
          <div class="ai-typing-dot"></div>
          <div class="ai-typing-dot"></div>
        </div>
      `;
      messagesContainer.insertAdjacentHTML('beforeend', indicatorHTML);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    hideTypingIndicator() {
      const indicator = document.getElementById('typing-indicator');
      if (indicator) {
        indicator.remove();
      }
    }

    async trackEvent(eventType, metadata = {}) {
      try {
        await fetch(`${API_BASE_URL}/widget/events`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
          },
          body: JSON.stringify({
            session_id: this.sessionId,
            event_type: eventType,
            metadata: {
              ...metadata,
              timestamp: new Date().toISOString(),
              user_agent: navigator.userAgent
            }
          })
        });
      } catch (error) {
        console.error('Failed to track event:', error);
      }
    }

    getOrCreateSessionId() {
      const storageKey = 'ai_chat_session_id';
      let sessionId = localStorage.getItem(storageKey);
      
      if (!sessionId) {
        sessionId = this.generateUUID();
        localStorage.setItem(storageKey, sessionId);
      }
      
      return sessionId;
    }

    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return text.replace(/[&<>"']/g, m => map[m]);
    }

    formatTime(date) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  }

  // Initialize widget when script loads
  window.AIChatWidget = AIChatWidget;
  
  // Auto-initialize if data attributes are present
  const scriptTag = document.currentScript;
  if (scriptTag && scriptTag.dataset.apiKey) {
    new AIChatWidget({
      apiKey: scriptTag.dataset.apiKey,
      customerEmail: scriptTag.dataset.customerEmail,
      customerName: scriptTag.dataset.customerName
    });
  }
})();